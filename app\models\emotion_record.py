"""
情绪分析记录数据模型
"""
from datetime import datetime
import json
from app import db

class EmotionRecord(db.Model):
    """情绪分析记录模型"""
    __tablename__ = 'emotion_records'
    
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.<PERSON>('users.id'), nullable=False, comment='用户ID')
    text_content = db.Column(db.Text, nullable=False, comment='分析文本内容')
    emotion_result = db.Column(db.JSON, nullable=False, comment='情绪分析结果(JSON格式)')
    dominant_emotion = db.Column(db.String(20), nullable=False, comment='主要情绪')
    confidence_score = db.Column(db.Numeric(5, 4), nullable=False, comment='置信度分数')
    analysis_time = db.Column(db.DateTime, default=datetime.utcnow, comment='分析时间')
    ip_address = db.Column(db.String(45), comment='IP地址')
    user_agent = db.Column(db.Text, comment='用户代理')
    
    def __init__(self, **kwargs):
        super(EmotionRecord, self).__init__(**kwargs)
    
    def set_emotion_result(self, result_dict):
        """设置情绪分析结果"""
        self.emotion_result = result_dict
        
        # 找出置信度最高的情绪作为主要情绪
        if result_dict and 'scores' in result_dict:
            max_score = 0
            dominant_emotion = None
            
            for emotion, score in result_dict['scores'].items():
                if score > max_score:
                    max_score = score
                    dominant_emotion = emotion
            
            self.dominant_emotion = dominant_emotion
            self.confidence_score = max_score
    
    def get_emotion_scores(self):
        """获取情绪分数字典"""
        if self.emotion_result and 'scores' in self.emotion_result:
            return self.emotion_result['scores']
        return {}
    
    def get_formatted_result(self):
        """获取格式化的分析结果"""
        scores = self.get_emotion_scores()
        if not scores:
            return {}
        
        # 按分数排序
        sorted_emotions = sorted(scores.items(), key=lambda x: x[1], reverse=True)
        
        return {
            'dominant_emotion': self.dominant_emotion,
            'confidence_score': float(self.confidence_score),
            'all_scores': dict(sorted_emotions),
            'analysis_time': self.analysis_time.isoformat() if self.analysis_time else None
        }
    
    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'user_id': self.user_id,
            'text_content': self.text_content,
            'emotion_result': self.emotion_result,
            'dominant_emotion': self.dominant_emotion,
            'confidence_score': float(self.confidence_score) if self.confidence_score else 0,
            'analysis_time': self.analysis_time.isoformat() if self.analysis_time else None,
            'ip_address': self.ip_address,
            'formatted_result': self.get_formatted_result()
        }
    
    @staticmethod
    def get_user_recent_records(user_id, limit=10):
        """获取用户最近的分析记录"""
        return EmotionRecord.query.filter_by(user_id=user_id)\
            .order_by(EmotionRecord.analysis_time.desc())\
            .limit(limit).all()
    
    @staticmethod
    def get_emotion_trend(user_id=None, days=30):
        """获取情绪趋势数据"""
        from datetime import datetime, timedelta
        from sqlalchemy import func, and_
        
        start_date = datetime.utcnow() - timedelta(days=days)
        
        query = db.session.query(
            func.date(EmotionRecord.analysis_time).label('date'),
            EmotionRecord.dominant_emotion,
            func.count(EmotionRecord.id).label('count')
        ).filter(EmotionRecord.analysis_time >= start_date)
        
        if user_id:
            query = query.filter(EmotionRecord.user_id == user_id)
        
        results = query.group_by(
            func.date(EmotionRecord.analysis_time),
            EmotionRecord.dominant_emotion
        ).all()
        
        # 组织数据
        trend_data = {}
        for date, emotion, count in results:
            date_str = date.strftime('%Y-%m-%d')
            if date_str not in trend_data:
                trend_data[date_str] = {}
            trend_data[date_str][emotion] = count
        
        return trend_data
    
    def __repr__(self):
        return f'<EmotionRecord {self.id}: {self.dominant_emotion}>'


class EmotionStatistic(db.Model):
    """情绪统计模型"""
    __tablename__ = 'emotion_statistics'
    
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), comment='用户ID(NULL表示全局统计)')
    date = db.Column(db.Date, nullable=False, comment='统计日期')
    fear_count = db.Column(db.Integer, default=0, comment='恐惧情绪次数')
    anger_count = db.Column(db.Integer, default=0, comment='愤怒情绪次数')
    disgust_count = db.Column(db.Integer, default=0, comment='厌恶情绪次数')
    like_count = db.Column(db.Integer, default=0, comment='喜好情绪次数')
    sadness_count = db.Column(db.Integer, default=0, comment='悲伤情绪次数')
    joy_count = db.Column(db.Integer, default=0, comment='高兴情绪次数')
    surprise_count = db.Column(db.Integer, default=0, comment='惊讶情绪次数')
    total_count = db.Column(db.Integer, default=0, comment='总分析次数')
    avg_confidence = db.Column(db.Numeric(5, 4), comment='平均置信度')
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    __table_args__ = (
        db.UniqueConstraint('user_id', 'date', name='unique_user_date'),
    )
    
    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'user_id': self.user_id,
            'date': self.date.isoformat() if self.date else None,
            'fear_count': self.fear_count,
            'anger_count': self.anger_count,
            'disgust_count': self.disgust_count,
            'like_count': self.like_count,
            'sadness_count': self.sadness_count,
            'joy_count': self.joy_count,
            'surprise_count': self.surprise_count,
            'total_count': self.total_count,
            'avg_confidence': float(self.avg_confidence) if self.avg_confidence else 0
        }
    
    def __repr__(self):
        return f'<EmotionStatistic {self.date}: {self.total_count} records>'
