{% extends "base.html" %}

{% block title %}专业分析报告 - {{ SYSTEM_NAME }}{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2>
                <i class="fas fa-chart-bar me-2"></i>专业分析报告
                <small class="text-muted">{{ current_user.department }} - {{ current_user.major }}</small>
            </h2>
            <div>
                <div class="btn-group" role="group">
                    <a href="?days=7" class="btn btn-outline-primary {{ 'active' if days == 7 else '' }}">7天</a>
                    <a href="?days=30" class="btn btn-outline-primary {{ 'active' if days == 30 else '' }}">30天</a>
                    <a href="?days=90" class="btn btn-outline-primary {{ 'active' if days == 90 else '' }}">90天</a>
                </div>
                <a href="{{ url_for('teacher.dashboard') }}" class="btn btn-outline-secondary ms-2">
                    <i class="fas fa-arrow-left me-2"></i>返回工作台
                </a>
            </div>
        </div>
    </div>
</div>

<!-- 总体概览 -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body text-center">
                <h4 class="mb-0">{{ students | length }}</h4>
                <p class="mb-0">专业学生总数</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body text-center">
                <h4 class="mb-0">{{ activity_stats | length }}</h4>
                <p class="mb-0">活跃学生数</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body text-center">
                <h4 class="mb-0">{{ emotion_stats | sum(attribute='count') }}</h4>
                <p class="mb-0">总分析次数</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-warning text-white">
            <div class="card-body text-center">
                <h4 class="mb-0">{{ ((activity_stats | length) / (students | length) * 100) | round(1) if students else 0 }}%</h4>
                <p class="mb-0">系统使用率</p>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- 整体情绪分布 -->
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-pie me-2"></i>专业整体情绪分布
                </h5>
            </div>
            <div class="card-body">
                {% if emotion_stats %}
                <canvas id="overallEmotionChart" width="400" height="400"></canvas>
                {% else %}
                <div class="text-center text-muted py-5">
                    <i class="fas fa-chart-pie fa-4x mb-3"></i>
                    <p>暂无情绪数据</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
    
    <!-- 年级对比 -->
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-bar me-2"></i>年级情绪对比
                </h5>
            </div>
            <div class="card-body">
                {% if grade_stats %}
                <canvas id="gradeComparisonChart" width="400" height="400"></canvas>
                {% else %}
                <div class="text-center text-muted py-5">
                    <i class="fas fa-chart-bar fa-4x mb-3"></i>
                    <p>暂无年级数据</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- 年级详细统计 -->
{% if grade_stats %}
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-graduation-cap me-2"></i>年级详细统计
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>年级</th>
                                <th>学生数</th>
                                <th>分析次数</th>
                                <th>人均分析</th>
                                <th>主要情绪</th>
                                <th>积极情绪比例</th>
                                <th>消极情绪比例</th>
                                <th>评估</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for grade, stats in grade_stats.items() %}
                            {% set total_emotions = stats.emotions.values() | sum %}
                            {% set positive = (stats.emotions.get('高兴', 0) + stats.emotions.get('喜好', 0)) %}
                            {% set negative = (stats.emotions.get('悲伤', 0) + stats.emotions.get('愤怒', 0) + stats.emotions.get('恐惧', 0) + stats.emotions.get('厌恶', 0)) %}
                            {% set positive_ratio = (positive / total_emotions * 100) if total_emotions > 0 else 0 %}
                            {% set negative_ratio = (negative / total_emotions * 100) if total_emotions > 0 else 0 %}
                            <tr>
                                <td><strong>{{ grade }}</strong></td>
                                <td>{{ stats.student_count }}</td>
                                <td>{{ stats.analysis_count }}</td>
                                <td>{{ (stats.analysis_count / stats.student_count) | round(1) if stats.student_count > 0 else 0 }}</td>
                                <td>
                                    {% if stats.emotions %}
                                    {% set dominant_emotion = stats.emotions | dictsort(by='value', reverse=true) | first %}
                                    {% set emotion_colors = {
                                        '高兴': 'warning',
                                        '悲伤': 'primary', 
                                        '愤怒': 'danger',
                                        '恐惧': 'dark',
                                        '厌恶': 'success',
                                        '喜好': 'info',
                                        '惊讶': 'secondary'
                                    } %}
                                    <span class="badge bg-{{ emotion_colors.get(dominant_emotion[0], 'secondary') }}">
                                        {{ dominant_emotion[0] }}
                                    </span>
                                    {% else %}
                                    -
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="progress" style="height: 20px;">
                                        <div class="progress-bar bg-success" style="width: {{ positive_ratio }}%">
                                            {{ positive_ratio | round(1) }}%
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <div class="progress" style="height: 20px;">
                                        <div class="progress-bar bg-danger" style="width: {{ negative_ratio }}%">
                                            {{ negative_ratio | round(1) }}%
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    {% if positive_ratio > 40 and negative_ratio < 30 %}
                                    <span class="badge bg-success">良好</span>
                                    {% elif positive_ratio > 20 and negative_ratio < 50 %}
                                    <span class="badge bg-warning">一般</span>
                                    {% elif total_emotions > 0 %}
                                    <span class="badge bg-danger">需关注</span>
                                    {% else %}
                                    <span class="badge bg-secondary">无数据</span>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- 活跃度排行 -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-trophy me-2"></i>学生活跃度排行 (TOP 20)
                </h5>
            </div>
            <div class="card-body">
                {% if activity_stats %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th width="10%">排名</th>
                                <th width="15%">学号</th>
                                <th width="20%">姓名</th>
                                <th width="15%">年级</th>
                                <th width="15%">分析次数</th>
                                <th width="15%">活跃度</th>
                                <th width="10%">操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for item in activity_stats %}
                            <tr>
                                <td>
                                    {% if loop.index <= 3 %}
                                    <span class="badge bg-warning">{{ loop.index }}</span>
                                    {% else %}
                                    <span class="badge bg-secondary">{{ loop.index }}</span>
                                    {% endif %}
                                </td>
                                <td>{{ item.student.student_id or '-' }}</td>
                                <td>{{ item.student.real_name or item.student.username }}</td>
                                <td>{{ item.student.grade or '-' }}</td>
                                <td>
                                    <span class="badge bg-primary">{{ item.count }}</span>
                                </td>
                                <td>
                                    {% if item.count >= 20 %}
                                    <span class="badge bg-success">很活跃</span>
                                    {% elif item.count >= 10 %}
                                    <span class="badge bg-info">活跃</span>
                                    {% elif item.count >= 5 %}
                                    <span class="badge bg-warning">一般</span>
                                    {% else %}
                                    <span class="badge bg-secondary">较少</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <a href="{{ url_for('teacher.student_detail', student_id=item.student.id, days=days) }}" 
                                       class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center text-muted py-5">
                    <i class="fas fa-users fa-4x mb-3"></i>
                    <h4>暂无活跃数据</h4>
                    <p>最近{{ days }}天内没有学生使用系统</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- 分析建议 -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card border-info">
            <div class="card-header bg-info text-white">
                <h5 class="mb-0">
                    <i class="fas fa-lightbulb me-2"></i>分析建议
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>系统使用情况：</h6>
                        <ul>
                            {% set usage_rate = ((activity_stats | length) / (students | length) * 100) if students else 0 %}
                            {% if usage_rate > 70 %}
                            <li class="text-success">系统使用率较高({{ usage_rate | round(1) }}%)，学生参与度良好</li>
                            {% elif usage_rate > 40 %}
                            <li class="text-warning">系统使用率中等({{ usage_rate | round(1) }}%)，可进一步推广</li>
                            {% else %}
                            <li class="text-danger">系统使用率较低({{ usage_rate | round(1) }}%)，需要加强宣传</li>
                            {% endif %}
                            
                            {% if activity_stats %}
                            <li>平均每位活跃学生分析{{ (emotion_stats | sum(attribute='count') / (activity_stats | length)) | round(1) }}次</li>
                            {% endif %}
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6>情绪健康状况：</h6>
                        <ul>
                            {% if emotion_stats %}
                            {% set total_emotions = emotion_stats | sum(attribute='count') %}
                            {% set positive_total = 0 %}
                            {% set negative_total = 0 %}
                            {% for stat in emotion_stats %}
                                {% if stat.dominant_emotion in ['高兴', '喜好'] %}
                                    {% set positive_total = positive_total + stat.count %}
                                {% elif stat.dominant_emotion in ['悲伤', '愤怒', '恐惧', '厌恶'] %}
                                    {% set negative_total = negative_total + stat.count %}
                                {% endif %}
                            {% endfor %}
                            {% set positive_ratio = (positive_total / total_emotions * 100) if total_emotions > 0 else 0 %}
                            {% set negative_ratio = (negative_total / total_emotions * 100) if total_emotions > 0 else 0 %}
                            
                            {% if positive_ratio > 40 %}
                            <li class="text-success">专业整体情绪状态良好，积极情绪占{{ positive_ratio | round(1) }}%</li>
                            {% elif negative_ratio > 50 %}
                            <li class="text-danger">需要关注学生心理健康，消极情绪占{{ negative_ratio | round(1) }}%</li>
                            {% else %}
                            <li class="text-info">专业情绪状态正常，需持续关注</li>
                            {% endif %}
                            
                            <li>建议定期组织心理健康活动</li>
                            <li>对消极情绪较多的学生进行个别关注</li>
                            {% else %}
                            <li class="text-muted">暂无足够数据进行评估</li>
                            {% endif %}
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
{% if emotion_stats %}
<script>
// 整体情绪分布图
const overallCtx = document.getElementById('overallEmotionChart').getContext('2d');
const overallChart = new Chart(overallCtx, {
    type: 'doughnut',
    data: {
        labels: [
            {% for stat in emotion_stats %}
            '{{ stat.dominant_emotion }}'{{ ',' if not loop.last else '' }}
            {% endfor %}
        ],
        datasets: [{
            data: [
                {% for stat in emotion_stats %}
                {{ stat.count }}{{ ',' if not loop.last else '' }}
                {% endfor %}
            ],
            backgroundColor: [
                '#FFD700', '#4169E1', '#FF4500', '#8B4513', 
                '#9ACD32', '#FF69B4', '#9370DB'
            ],
            borderWidth: 2,
            borderColor: '#fff'
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'bottom'
            }
        }
    }
});
</script>
{% endif %}

{% if grade_stats %}
<script>
// 年级对比图
const gradeCtx = document.getElementById('gradeComparisonChart').getContext('2d');
const gradeData = {{ grade_stats | tojson }};
const grades = Object.keys(gradeData);
const emotions = ['高兴', '悲伤', '愤怒', '恐惧', '厌恶', '喜好', '惊讶'];
const colors = ['#FFD700', '#4169E1', '#FF4500', '#8B4513', '#9ACD32', '#FF69B4', '#9370DB'];

const datasets = emotions.map((emotion, index) => ({
    label: emotion,
    data: grades.map(grade => gradeData[grade].emotions[emotion] || 0),
    backgroundColor: colors[index],
    borderColor: colors[index],
    borderWidth: 1
}));

const gradeChart = new Chart(gradeCtx, {
    type: 'bar',
    data: {
        labels: grades,
        datasets: datasets
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'bottom'
            }
        },
        scales: {
            x: {
                stacked: true
            },
            y: {
                stacked: true,
                beginAtZero: true
            }
        }
    }
});
</script>
{% endif %}
{% endblock %}
