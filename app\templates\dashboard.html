{% extends "base.html" %}

{% block title %}仪表板 - {{ SYSTEM_NAME }}{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h2 class="mb-4">
            <i class="fas fa-tachometer-alt me-2"></i>个人仪表板
            <small class="text-muted">欢迎，{{ current_user.real_name or current_user.username }}！</small>
        </h2>
    </div>
</div>

<!-- 统计卡片 -->
<div class="row mb-4">
    <div class="col-md-3 mb-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0">{{ analysis_count }}</h4>
                        <p class="mb-0">本月分析次数</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-search fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0">{{ emotion_stats.get('高兴', 0) }}</h4>
                        <p class="mb-0">高兴情绪</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-smile fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0">{{ emotion_stats.get('悲伤', 0) }}</h4>
                        <p class="mb-0">悲伤情绪</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-frown fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0">{{ emotion_stats.values() | sum }}</h4>
                        <p class="mb-0">总情绪记录</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-chart-pie fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- 快速分析 -->
    <div class="col-md-8 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-search me-2"></i>快速情绪分析
                </h5>
            </div>
            <div class="card-body">
                <form action="{{ url_for('emotion.analyze') }}" method="POST">
                    <div class="mb-3">
                        <textarea class="form-control" name="text" rows="4" 
                                placeholder="请输入要分析的文本内容..." required></textarea>
                    </div>
                    <div class="d-flex justify-content-between">
                        <small class="text-muted">最多支持500个字符</small>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search me-2"></i>开始分析
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- 情绪分布 -->
    <div class="col-md-4 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-pie me-2"></i>情绪分布
                </h5>
            </div>
            <div class="card-body">
                {% if emotion_stats %}
                <canvas id="emotionChart" width="300" height="300"></canvas>
                {% else %}
                <div class="text-center text-muted">
                    <i class="fas fa-chart-pie fa-3x mb-3"></i>
                    <p>暂无数据</p>
                    <a href="{{ url_for('emotion.analyze') }}" class="btn btn-sm btn-outline-primary">
                        开始分析
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- 最近分析记录 -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-history me-2"></i>最近分析记录
                </h5>
                <a href="{{ url_for('emotion.history') }}" class="btn btn-sm btn-outline-primary">
                    查看全部
                </a>
            </div>
            <div class="card-body">
                {% if recent_records %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>文本内容</th>
                                <th>主要情绪</th>
                                <th>置信度</th>
                                <th>分析时间</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for record in recent_records %}
                            <tr>
                                <td>
                                    <div style="max-width: 200px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">
                                        {{ record.text_content }}
                                    </div>
                                </td>
                                <td>
                                    <span class="badge bg-primary">{{ record.dominant_emotion }}</span>
                                </td>
                                <td>
                                    <div class="progress" style="height: 20px;">
                                        <div class="progress-bar" role="progressbar" 
                                             style="width: {{ (record.confidence_score * 100) | round(1) }}%">
                                            {{ (record.confidence_score * 100) | round(1) }}%
                                        </div>
                                    </div>
                                </td>
                                <td>{{ record.analysis_time.strftime('%m-%d %H:%M') }}</td>
                                <td>
                                    <a href="{{ url_for('emotion.result', record_id=record.id) }}" 
                                       class="btn btn-sm btn-outline-info">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center text-muted py-4">
                    <i class="fas fa-inbox fa-3x mb-3"></i>
                    <p>暂无分析记录</p>
                    <a href="{{ url_for('emotion.analyze') }}" class="btn btn-primary">
                        <i class="fas fa-search me-2"></i>开始第一次分析
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
{% if emotion_stats %}
<script>
// 情绪分布饼图
const ctx = document.getElementById('emotionChart').getContext('2d');
const emotionChart = new Chart(ctx, {
    type: 'doughnut',
    data: {
        labels: {{ emotion_stats.keys() | list | tojson }},
        datasets: [{
            data: {{ emotion_stats.values() | list | tojson }},
            backgroundColor: [
                '#FFD700', // 高兴 - 金色
                '#4169E1', // 悲伤 - 皇家蓝
                '#FF4500', // 愤怒 - 橙红色
                '#8B4513', // 恐惧 - 棕色
                '#9ACD32', // 厌恶 - 黄绿色
                '#FF69B4', // 喜好 - 热粉色
                '#9370DB'  // 惊讶 - 中紫色
            ],
            borderWidth: 2,
            borderColor: '#fff'
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'bottom',
                labels: {
                    padding: 10,
                    usePointStyle: true
                }
            }
        }
    }
});
</script>
{% endif %}
{% endblock %}
