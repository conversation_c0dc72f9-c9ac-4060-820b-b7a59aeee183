{% extends "base.html" %}

{% block title %}管理员仪表板 - {{ SYSTEM_NAME }}{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h2 class="mb-4">
            <i class="fas fa-tachometer-alt me-2"></i>管理员仪表板
        </h2>
    </div>
</div>

<!-- 统计卡片 -->
<div class="row mb-4">
    <div class="col-md-3 mb-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0">{{ total_users }}</h4>
                        <p class="mb-0">注册用户</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-users fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0">{{ total_records }}</h4>
                        <p class="mb-0">分析记录</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-chart-bar fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0">{{ today_records }}</h4>
                        <p class="mb-0">今日分析</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-calendar-day fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0">{{ active_users | length }}</h4>
                        <p class="mb-0">活跃用户</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-user-check fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- 每日分析趋势 -->
    <div class="col-md-8 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-line me-2"></i>最近7天分析趋势
                </h5>
            </div>
            <div class="card-body">
                <canvas id="dailyTrendChart" width="400" height="200"></canvas>
            </div>
        </div>
    </div>
    
    <!-- 情绪分布 -->
    <div class="col-md-4 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-pie me-2"></i>全局情绪分布
                </h5>
            </div>
            <div class="card-body">
                <canvas id="emotionDistChart" width="300" height="300"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- 活跃用户列表 -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-star me-2"></i>本周活跃用户 (TOP 10)
                </h5>
                <a href="{{ url_for('admin.users') }}" class="btn btn-sm btn-outline-primary">
                    查看全部用户
                </a>
            </div>
            <div class="card-body">
                {% if active_users %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>排名</th>
                                <th>用户名</th>
                                <th>真实姓名</th>
                                <th>院系</th>
                                <th>专业</th>
                                <th>分析次数</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for user in active_users %}
                            <tr>
                                <td>
                                    <span class="badge bg-primary">{{ loop.index }}</span>
                                </td>
                                <td>{{ user.username }}</td>
                                <td>{{ user.real_name or '-' }}</td>
                                <td>{{ user.department or '-' }}</td>
                                <td>{{ user.major or '-' }}</td>
                                <td>
                                    <span class="badge bg-success">{{ user.analysis_count }}</span>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center text-muted py-4">
                    <i class="fas fa-users fa-3x mb-3"></i>
                    <p>暂无活跃用户数据</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// 每日趋势图
const dailyCtx = document.getElementById('dailyTrendChart').getContext('2d');
const dailyChart = new Chart(dailyCtx, {
    type: 'line',
    data: {
        labels: [
            {% for stat in daily_stats %}
            '{{ stat.date.strftime("%m-%d") }}'{{ ',' if not loop.last else '' }}
            {% endfor %}
        ],
        datasets: [{
            label: '分析次数',
            data: [
                {% for stat in daily_stats %}
                {{ stat.count }}{{ ',' if not loop.last else '' }}
                {% endfor %}
            ],
            borderColor: '#007bff',
            backgroundColor: 'rgba(0, 123, 255, 0.1)',
            tension: 0.4,
            fill: true
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                display: false
            }
        },
        scales: {
            y: {
                beginAtZero: true,
                ticks: {
                    stepSize: 1
                }
            }
        }
    }
});

// 情绪分布饼图
const emotionCtx = document.getElementById('emotionDistChart').getContext('2d');
const emotionChart = new Chart(emotionCtx, {
    type: 'doughnut',
    data: {
        labels: [
            {% for stat in emotion_stats %}
            '{{ stat.dominant_emotion }}'{{ ',' if not loop.last else '' }}
            {% endfor %}
        ],
        datasets: [{
            data: [
                {% for stat in emotion_stats %}
                {{ stat.count }}{{ ',' if not loop.last else '' }}
                {% endfor %}
            ],
            backgroundColor: [
                '#FFD700', '#4169E1', '#FF4500', '#8B4513', 
                '#9ACD32', '#FF69B4', '#9370DB'
            ],
            borderWidth: 2,
            borderColor: '#fff'
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'bottom',
                labels: {
                    padding: 10,
                    usePointStyle: true
                }
            }
        }
    }
});
</script>
{% endblock %}
