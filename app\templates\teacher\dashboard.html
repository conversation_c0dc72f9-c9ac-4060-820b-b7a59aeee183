{% extends "base.html" %}

{% block title %}教师工作台 - {{ SYSTEM_NAME }}{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h2 class="mb-4">
            <i class="fas fa-chalkboard-teacher me-2"></i>教师工作台
            <small class="text-muted">{{ current_user.department }} - {{ current_user.major }}</small>
        </h2>
    </div>
</div>

<!-- 统计卡片 -->
<div class="row mb-4">
    <div class="col-md-3 mb-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0">{{ total_students }}</h4>
                        <p class="mb-0">本专业学生</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-users fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0">{{ total_records }}</h4>
                        <p class="mb-0">总分析记录</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-chart-bar fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0">{{ today_records }}</h4>
                        <p class="mb-0">今日分析</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-calendar-day fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0">{{ concern_students | length }}</h4>
                        <p class="mb-0">需要关注</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-exclamation-triangle fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- 分析趋势图 -->
    <div class="col-md-8 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-line me-2"></i>最近7天分析趋势
                </h5>
            </div>
            <div class="card-body">
                {% if daily_stats %}
                <canvas id="dailyTrendChart" width="400" height="200"></canvas>
                {% else %}
                <div class="text-center text-muted py-4">
                    <i class="fas fa-chart-line fa-3x mb-3"></i>
                    <p>暂无数据</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
    
    <!-- 情绪分布 -->
    <div class="col-md-4 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-pie me-2"></i>专业情绪分布
                </h5>
            </div>
            <div class="card-body">
                {% if emotion_stats %}
                <canvas id="emotionDistChart" width="300" height="300"></canvas>
                {% else %}
                <div class="text-center text-muted py-4">
                    <i class="fas fa-chart-pie fa-3x mb-3"></i>
                    <p>暂无数据</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- 需要关注的学生 -->
{% if concern_students %}
<div class="row mb-4">
    <div class="col-12">
        <div class="card border-warning">
            <div class="card-header bg-warning text-dark">
                <h5 class="mb-0">
                    <i class="fas fa-exclamation-triangle me-2"></i>需要关注的学生
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    {% for item in concern_students %}
                    <div class="col-md-6 mb-3">
                        <div class="card border-warning">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h6 class="mb-1">{{ item.student.real_name }}</h6>
                                        <p class="text-muted mb-1">{{ item.student.student_id }}</p>
                                        <small class="text-danger">
                                            消极情绪占比: {{ (item.negative_ratio * 100) | round(1) }}%
                                        </small>
                                    </div>
                                    <div class="text-end">
                                        <span class="badge bg-warning">{{ item.total_count }}次分析</span>
                                        <br>
                                        <a href="{{ url_for('teacher.student_detail', student_id=item.student.id) }}" 
                                           class="btn btn-sm btn-outline-primary mt-2">
                                            查看详情
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- 活跃学生列表 -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-star me-2"></i>本周活跃学生 (TOP 10)
                </h5>
                <a href="{{ url_for('teacher.students') }}" class="btn btn-sm btn-outline-primary">
                    查看全部学生
                </a>
            </div>
            <div class="card-body">
                {% if active_students %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>排名</th>
                                <th>学号</th>
                                <th>姓名</th>
                                <th>分析次数</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for student in active_students %}
                            <tr>
                                <td>
                                    <span class="badge bg-primary">{{ loop.index }}</span>
                                </td>
                                <td>{{ student.student_id or '-' }}</td>
                                <td>{{ student.real_name or student.username }}</td>
                                <td>
                                    <span class="badge bg-success">{{ student.analysis_count }}</span>
                                </td>
                                <td>
                                    <a href="{{ url_for('teacher.student_detail', student_id=student.id) }}" 
                                       class="btn btn-sm btn-outline-info">
                                        <i class="fas fa-eye"></i> 查看
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center text-muted py-4">
                    <i class="fas fa-users fa-3x mb-3"></i>
                    <p>暂无活跃学生数据</p>
                    <a href="{{ url_for('teacher.students') }}" class="btn btn-primary">
                        查看学生列表
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- 快捷操作 -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-tools me-2"></i>快捷操作
                </h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-md-3 mb-3">
                        <a href="{{ url_for('teacher.students') }}" class="btn btn-outline-primary btn-lg w-100">
                            <i class="fas fa-users fa-2x mb-2"></i><br>
                            学生管理
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="{{ url_for('teacher.analytics') }}" class="btn btn-outline-success btn-lg w-100">
                            <i class="fas fa-chart-bar fa-2x mb-2"></i><br>
                            分析报告
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="{{ url_for('teacher.export_data') }}" class="btn btn-outline-info btn-lg w-100">
                            <i class="fas fa-download fa-2x mb-2"></i><br>
                            导出数据
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="{{ url_for('auth.profile') }}" class="btn btn-outline-secondary btn-lg w-100">
                            <i class="fas fa-user fa-2x mb-2"></i><br>
                            个人资料
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
{% if daily_stats %}
<script>
// 每日趋势图
const dailyCtx = document.getElementById('dailyTrendChart').getContext('2d');
const dailyChart = new Chart(dailyCtx, {
    type: 'line',
    data: {
        labels: [
            {% for stat in daily_stats %}
            '{{ stat.date.strftime("%m-%d") }}'{{ ',' if not loop.last else '' }}
            {% endfor %}
        ],
        datasets: [{
            label: '分析次数',
            data: [
                {% for stat in daily_stats %}
                {{ stat.count }}{{ ',' if not loop.last else '' }}
                {% endfor %}
            ],
            borderColor: '#007bff',
            backgroundColor: 'rgba(0, 123, 255, 0.1)',
            tension: 0.4,
            fill: true
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                display: false
            }
        },
        scales: {
            y: {
                beginAtZero: true,
                ticks: {
                    stepSize: 1
                }
            }
        }
    }
});
</script>
{% endif %}

{% if emotion_stats %}
<script>
// 情绪分布饼图
const emotionCtx = document.getElementById('emotionDistChart').getContext('2d');
const emotionChart = new Chart(emotionCtx, {
    type: 'doughnut',
    data: {
        labels: [
            {% for stat in emotion_stats %}
            '{{ stat.dominant_emotion }}'{{ ',' if not loop.last else '' }}
            {% endfor %}
        ],
        datasets: [{
            data: [
                {% for stat in emotion_stats %}
                {{ stat.count }}{{ ',' if not loop.last else '' }}
                {% endfor %}
            ],
            backgroundColor: [
                '#FFD700', '#4169E1', '#FF4500', '#8B4513', 
                '#9ACD32', '#FF69B4', '#9370DB'
            ],
            borderWidth: 2,
            borderColor: '#fff'
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'bottom',
                labels: {
                    padding: 10,
                    usePointStyle: true
                }
            }
        }
    }
});
</script>
{% endif %}
{% endblock %}
