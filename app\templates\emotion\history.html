{% extends "base.html" %}

{% block title %}分析历史 - {{ SYSTEM_NAME }}{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2>
                <i class="fas fa-history me-2"></i>我的分析历史
            </h2>
            <div>
                <a href="{{ url_for('emotion.analyze') }}" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>新建分析
                </a>
            </div>
        </div>
    </div>
</div>

{% if records.items %}
<!-- 分析记录列表 -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th width="5%">#</th>
                                <th width="40%">文本内容</th>
                                <th width="15%">主要情绪</th>
                                <th width="15%">置信度</th>
                                <th width="15%">分析时间</th>
                                <th width="10%">操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for record in records.items %}
                            <tr>
                                <td>{{ loop.index + (records.page - 1) * records.per_page }}</td>
                                <td>
                                    <div style="max-width: 300px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;" 
                                         title="{{ record.text_content }}">
                                        {{ record.text_content }}
                                    </div>
                                </td>
                                <td>
                                    {% set emotion_colors = {
                                        '高兴': 'warning',
                                        '悲伤': 'primary', 
                                        '愤怒': 'danger',
                                        '恐惧': 'dark',
                                        '厌恶': 'success',
                                        '喜好': 'info',
                                        '惊讶': 'secondary'
                                    } %}
                                    <span class="badge bg-{{ emotion_colors.get(record.dominant_emotion, 'secondary') }}">
                                        {{ record.dominant_emotion }}
                                    </span>
                                </td>
                                <td>
                                    <div class="progress" style="height: 20px;">
                                        <div class="progress-bar bg-{{ emotion_colors.get(record.dominant_emotion, 'secondary') }}" 
                                             role="progressbar" 
                                             style="width: {{ (record.confidence_score * 100) | round(1) }}%">
                                            {{ (record.confidence_score * 100) | round(1) }}%
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <small>{{ record.analysis_time.strftime('%Y-%m-%d') }}</small><br>
                                    <small class="text-muted">{{ record.analysis_time.strftime('%H:%M:%S') }}</small>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm" role="group">
                                        <a href="{{ url_for('emotion.result', record_id=record.id) }}" 
                                           class="btn btn-outline-info" title="查看详情">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <form method="POST" action="{{ url_for('emotion.delete_record', record_id=record.id) }}" 
                                              style="display: inline;" 
                                              onsubmit="return confirm('确定要删除这条记录吗？')">
                                            <button type="submit" class="btn btn-outline-danger" title="删除记录">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </form>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 分页导航 -->
{% if records.pages > 1 %}
<div class="row mt-4">
    <div class="col-12">
        <nav aria-label="分析历史分页">
            <ul class="pagination justify-content-center">
                {% if records.has_prev %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for('emotion.history', page=records.prev_num) }}">
                        <i class="fas fa-chevron-left"></i> 上一页
                    </a>
                </li>
                {% endif %}
                
                {% for page_num in records.iter_pages() %}
                    {% if page_num %}
                        {% if page_num != records.page %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('emotion.history', page=page_num) }}">{{ page_num }}</a>
                        </li>
                        {% else %}
                        <li class="page-item active">
                            <span class="page-link">{{ page_num }}</span>
                        </li>
                        {% endif %}
                    {% else %}
                    <li class="page-item disabled">
                        <span class="page-link">…</span>
                    </li>
                    {% endif %}
                {% endfor %}
                
                {% if records.has_next %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for('emotion.history', page=records.next_num) }}">
                        下一页 <i class="fas fa-chevron-right"></i>
                    </a>
                </li>
                {% endif %}
            </ul>
        </nav>
        
        <div class="text-center text-muted">
            <small>
                第 {{ records.page }} 页，共 {{ records.pages }} 页，
                总共 {{ records.total }} 条记录
            </small>
        </div>
    </div>
</div>
{% endif %}

{% else %}
<!-- 空状态 -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-body text-center py-5">
                <i class="fas fa-inbox fa-4x text-muted mb-4"></i>
                <h4 class="text-muted mb-3">暂无分析记录</h4>
                <p class="text-muted mb-4">您还没有进行过情绪分析，开始您的第一次分析吧！</p>
                <a href="{{ url_for('emotion.analyze') }}" class="btn btn-primary btn-lg">
                    <i class="fas fa-search me-2"></i>开始分析
                </a>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- 统计信息 -->
<div class="row mt-4">
    <div class="col-md-3 mb-3">
        <div class="card bg-primary text-white">
            <div class="card-body text-center">
                <i class="fas fa-chart-bar fa-2x mb-2"></i>
                <h5 class="mb-0">{{ records.total }}</h5>
                <small>总分析次数</small>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card bg-success text-white">
            <div class="card-body text-center">
                <i class="fas fa-calendar fa-2x mb-2"></i>
                <h5 class="mb-0">{{ current_user.get_emotion_count(days=7) }}</h5>
                <small>本周分析</small>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card bg-info text-white">
            <div class="card-body text-center">
                <i class="fas fa-clock fa-2x mb-2"></i>
                <h5 class="mb-0">{{ current_user.get_emotion_count(days=1) }}</h5>
                <small>今日分析</small>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card bg-warning text-white">
            <div class="card-body text-center">
                <i class="fas fa-heart fa-2x mb-2"></i>
                <h5 class="mb-0">{{ (100 - current_user.get_emotion_count(days=1)) }}</h5>
                <small>今日剩余次数</small>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// 自动刷新页面（可选）
// setInterval(function() {
//     location.reload();
// }, 300000); // 5分钟刷新一次
</script>
{% endblock %}
