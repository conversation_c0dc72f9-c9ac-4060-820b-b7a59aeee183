"""
情绪分析服务
"""
import os
import torch
import logging
from datetime import datetime
from transformers import AutoTokenizer, AutoModelForSequenceClassification
from flask import current_app
from app import db
from app.models.emotion_record import EmotionRecord

logger = logging.getLogger(__name__)

class EmotionAnalysisService:
    """情绪分析服务类"""
    
    def __init__(self):
        self.model = None
        self.tokenizer = None
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.emotion_labels = {
            0: "恐惧",
            1: "愤怒", 
            2: "厌恶",
            3: "喜好",
            4: "悲伤",
            5: "高兴",
            6: "惊讶"
        }
    
    def load_model(self):
        """加载StructBERT模型"""
        try:
            model_path = current_app.config['MODEL_PATH']
            
            if not os.path.exists(model_path):
                raise FileNotFoundError(f"模型路径不存在: {model_path}")
            
            logger.info(f"正在加载模型: {model_path}")
            
            # 加载tokenizer和模型
            self.tokenizer = AutoTokenizer.from_pretrained(model_path)
            self.model = AutoModelForSequenceClassification.from_pretrained(model_path)
            
            # 移动到设备
            self.model.to(self.device)
            self.model.eval()
            
            logger.info(f"模型加载成功，使用设备: {self.device}")
            return True
            
        except Exception as e:
            logger.error(f"模型加载失败: {str(e)}")
            return False
    
    def preprocess_text(self, text):
        """文本预处理"""
        if not text or not isinstance(text, str):
            return ""
        
        # 基本清理
        text = text.strip()
        
        # 长度限制
        max_length = current_app.config.get('MAX_TEXT_LENGTH', 500)
        if len(text) > max_length:
            text = text[:max_length]
        
        return text
    
    def predict_emotion(self, text):
        """预测文本情绪"""
        try:
            # 检查模型是否已加载
            if self.model is None or self.tokenizer is None:
                if not self.load_model():
                    raise Exception("模型加载失败")
            
            # 预处理文本
            processed_text = self.preprocess_text(text)
            if not processed_text:
                raise ValueError("输入文本为空或无效")
            
            # 编码文本
            inputs = self.tokenizer(
                processed_text,
                return_tensors="pt",
                truncation=True,
                padding=True,
                max_length=512
            )
            
            # 移动到设备
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # 模型推理
            with torch.no_grad():
                outputs = self.model(**inputs)
                logits = outputs.logits
                
                # 计算概率
                probabilities = torch.softmax(logits, dim=-1)
                scores = probabilities.cpu().numpy()[0]
            
            # 构建结果
            emotion_scores = {}
            for idx, score in enumerate(scores):
                emotion = self.emotion_labels[idx]
                emotion_scores[emotion] = float(score)
            
            # 找出主要情绪
            dominant_emotion = max(emotion_scores.items(), key=lambda x: x[1])
            
            result = {
                'text': processed_text,
                'scores': emotion_scores,
                'dominant_emotion': dominant_emotion[0],
                'confidence': dominant_emotion[1],
                'model_info': {
                    'model_path': current_app.config['MODEL_PATH'],
                    'device': str(self.device)
                }
            }
            
            logger.info(f"情绪分析完成: {dominant_emotion[0]} ({dominant_emotion[1]:.4f})")
            return result
            
        except Exception as e:
            logger.error(f"情绪分析失败: {str(e)}")
            raise Exception(f"情绪分析失败: {str(e)}")
    
    def analyze_and_save(self, text, user_id, ip_address=None, user_agent=None):
        """分析情绪并保存记录"""
        try:
            # 执行情绪分析
            result = self.predict_emotion(text)
            
            # 创建记录
            record = EmotionRecord(
                user_id=user_id,
                text_content=text,
                ip_address=ip_address,
                user_agent=user_agent
            )
            
            # 设置分析结果
            record.set_emotion_result(result)
            
            # 保存到数据库
            db.session.add(record)
            db.session.commit()
            
            logger.info(f"情绪分析记录已保存: ID={record.id}")
            return record
            
        except Exception as e:
            db.session.rollback()
            logger.error(f"保存情绪分析记录失败: {str(e)}")
            raise
    
    def batch_analyze(self, texts, user_id):
        """批量分析情绪"""
        results = []
        
        for text in texts:
            try:
                result = self.predict_emotion(text)
                results.append({
                    'text': text,
                    'result': result,
                    'success': True
                })
            except Exception as e:
                results.append({
                    'text': text,
                    'error': str(e),
                    'success': False
                })
        
        return results
    
    def get_model_info(self):
        """获取模型信息"""
        return {
            'model_loaded': self.model is not None,
            'device': str(self.device),
            'emotion_labels': self.emotion_labels,
            'model_path': current_app.config.get('MODEL_PATH', ''),
            'max_text_length': current_app.config.get('MAX_TEXT_LENGTH', 500)
        }

# 全局服务实例
emotion_service = EmotionAnalysisService()
