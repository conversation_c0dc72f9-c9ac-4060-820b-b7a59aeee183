{% extends "base.html" %}

{% block title %}学生管理 - {{ SYSTEM_NAME }}{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2>
                <i class="fas fa-users me-2"></i>学生管理
                <small class="text-muted">{{ current_user.department }} - {{ current_user.major }}</small>
            </h2>
            <div>
                <a href="{{ url_for('teacher.dashboard') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-2"></i>返回工作台
                </a>
            </div>
        </div>
    </div>
</div>

<!-- 统计信息 -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body text-center">
                <h4 class="mb-0">{{ students.total }}</h4>
                <p class="mb-0">总学生数</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body text-center">
                <h4 class="mb-0">
                    {{ students.items | selectattr('week_analysis', 'gt', 0) | list | length }}
                </h4>
                <p class="mb-0">本周活跃</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body text-center">
                <h4 class="mb-0">
                    {{ students.items | selectattr('total_analysis', 'gt', 0) | list | length }}
                </h4>
                <p class="mb-0">已使用系统</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-warning text-white">
            <div class="card-body text-center">
                <h4 class="mb-0">
                    {{ students.items | selectattr('total_analysis', 'eq', 0) | list | length }}
                </h4>
                <p class="mb-0">未使用系统</p>
            </div>
        </div>
    </div>
</div>

<!-- 学生列表 -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-list me-2"></i>学生列表
                </h5>
            </div>
            <div class="card-body">
                {% if students.items %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>学号</th>
                                <th>姓名</th>
                                <th>年级</th>
                                <th>总分析次数</th>
                                <th>本周分析</th>
                                <th>最近情绪</th>
                                <th>最后活动</th>
                                <th>状态</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for student in students.items %}
                            <tr>
                                <td>
                                    <strong>{{ student.student_id or '-' }}</strong>
                                </td>
                                <td>
                                    <div>
                                        <strong>{{ student.real_name or student.username }}</strong>
                                        <br>
                                        <small class="text-muted">{{ student.username }}</small>
                                    </div>
                                </td>
                                <td>
                                    <span class="badge bg-secondary">{{ student.grade or '-' }}</span>
                                </td>
                                <td>
                                    <span class="badge bg-{{ 'success' if student.total_analysis > 10 else 'primary' if student.total_analysis > 0 else 'secondary' }}">
                                        {{ student.total_analysis }}
                                    </span>
                                </td>
                                <td>
                                    <span class="badge bg-{{ 'success' if student.week_analysis > 3 else 'warning' if student.week_analysis > 0 else 'secondary' }}">
                                        {{ student.week_analysis }}
                                    </span>
                                </td>
                                <td>
                                    {% if student.recent_emotion %}
                                    {% set emotion_colors = {
                                        '高兴': 'warning',
                                        '悲伤': 'primary', 
                                        '愤怒': 'danger',
                                        '恐惧': 'dark',
                                        '厌恶': 'success',
                                        '喜好': 'info',
                                        '惊讶': 'secondary'
                                    } %}
                                    <span class="badge bg-{{ emotion_colors.get(student.recent_emotion, 'secondary') }}">
                                        {{ student.recent_emotion }}
                                    </span>
                                    {% else %}
                                    <span class="text-muted">-</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if student.recent_time %}
                                    <small>
                                        {{ student.recent_time.strftime('%m-%d') }}<br>
                                        <span class="text-muted">{{ student.recent_time.strftime('%H:%M') }}</span>
                                    </small>
                                    {% else %}
                                    <span class="text-muted">从未使用</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if student.total_analysis == 0 %}
                                    <span class="badge bg-secondary">未激活</span>
                                    {% elif student.week_analysis == 0 %}
                                    <span class="badge bg-warning">不活跃</span>
                                    {% elif student.week_analysis > 5 %}
                                    <span class="badge bg-success">很活跃</span>
                                    {% else %}
                                    <span class="badge bg-info">正常</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm" role="group">
                                        <a href="{{ url_for('teacher.student_detail', student_id=student.id) }}" 
                                           class="btn btn-outline-primary" title="查看详情">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        {% if student.total_analysis > 0 %}
                                        <a href="{{ url_for('teacher.student_detail', student_id=student.id, days=7) }}" 
                                           class="btn btn-outline-info" title="本周数据">
                                            <i class="fas fa-chart-line"></i>
                                        </a>
                                        {% endif %}
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                
                <!-- 分页导航 -->
                {% if students.pages > 1 %}
                <nav aria-label="学生列表分页" class="mt-4">
                    <ul class="pagination justify-content-center">
                        {% if students.has_prev %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('teacher.students', page=students.prev_num) }}">
                                <i class="fas fa-chevron-left"></i> 上一页
                            </a>
                        </li>
                        {% endif %}
                        
                        {% for page_num in students.iter_pages() %}
                            {% if page_num %}
                                {% if page_num != students.page %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('teacher.students', page=page_num) }}">{{ page_num }}</a>
                                </li>
                                {% else %}
                                <li class="page-item active">
                                    <span class="page-link">{{ page_num }}</span>
                                </li>
                                {% endif %}
                            {% else %}
                            <li class="page-item disabled">
                                <span class="page-link">…</span>
                            </li>
                            {% endif %}
                        {% endfor %}
                        
                        {% if students.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('teacher.students', page=students.next_num) }}">
                                下一页 <i class="fas fa-chevron-right"></i>
                            </a>
                        </li>
                        {% endif %}
                    </ul>
                </nav>
                
                <div class="text-center text-muted">
                    <small>
                        第 {{ students.page }} 页，共 {{ students.pages }} 页，
                        总共 {{ students.total }} 名学生
                    </small>
                </div>
                {% endif %}
                
                {% else %}
                <div class="text-center text-muted py-5">
                    <i class="fas fa-users fa-4x mb-4"></i>
                    <h4>暂无学生数据</h4>
                    <p>当前专业还没有学生注册使用系统</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- 操作提示 -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card border-info">
            <div class="card-header bg-info text-white">
                <h6 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>使用说明
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>状态说明：</h6>
                        <ul class="list-unstyled">
                            <li><span class="badge bg-secondary me-2">未激活</span>从未使用过系统</li>
                            <li><span class="badge bg-warning me-2">不活跃</span>本周未使用系统</li>
                            <li><span class="badge bg-info me-2">正常</span>本周使用1-5次</li>
                            <li><span class="badge bg-success me-2">很活跃</span>本周使用5次以上</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6>操作说明：</h6>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-eye text-primary me-2"></i>查看学生详细信息和分析记录</li>
                            <li><i class="fas fa-chart-line text-info me-2"></i>查看学生本周情绪趋势</li>
                            <li>点击学生姓名可快速查看基本信息</li>
                            <li>关注"不活跃"和消极情绪较多的学生</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.table td {
    vertical-align: middle;
}

.btn-group-sm .btn {
    padding: 0.25rem 0.5rem;
}

.badge {
    font-size: 0.75em;
}

@media (max-width: 768px) {
    .table-responsive {
        font-size: 0.875rem;
    }
    
    .btn-group-sm .btn {
        padding: 0.125rem 0.25rem;
    }
}
</style>
{% endblock %}
