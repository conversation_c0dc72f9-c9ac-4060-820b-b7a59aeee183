# 黔南民族师范学院学生情绪分析系统 - 快速启动指南

## 系统要求

### 软件环境
- **Python**: 3.8 或更高版本
- **MySQL**: 8.0 或更高版本
- **操作系统**: Windows 10/11, macOS, Linux

### 硬件建议
- **内存**: 至少 8GB RAM
- **存储**: 至少 5GB 可用空间
- **GPU**: 可选，用于加速模型推理

## 安装步骤

### 1. 克隆项目
```bash
git clone <项目地址>
cd emo2
```

### 2. 运行安装脚本
```bash
python setup.py
```

安装脚本将自动完成：
- 创建必要的目录结构
- 生成环境配置文件
- 检查模型文件
- 安装Python依赖

### 3. 配置数据库

#### 3.1 创建数据库
```sql
-- 登录MySQL
mysql -u root -p

-- 创建数据库
CREATE DATABASE emotion_analysis CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建用户（可选）
CREATE USER 'emotion_user'@'localhost' IDENTIFIED BY 'your_password';
GRANT ALL PRIVILEGES ON emotion_analysis.* TO 'emotion_user'@'localhost';
FLUSH PRIVILEGES;
```

#### 3.2 导入数据库结构
```bash
mysql -u root -p emotion_analysis < config/database.sql
```

### 4. 配置环境变量

编辑 `.env` 文件，修改数据库连接信息：

```env
# 数据库配置
MYSQL_HOST=localhost
MYSQL_PORT=3306
MYSQL_USER=root
MYSQL_PASSWORD=your_password
MYSQL_DATABASE=emotion_analysis
```

### 5. 启动系统
```bash
python run.py
```

系统将在 `http://localhost:5000` 启动。

## 首次使用

### 1. 访问系统
打开浏览器，访问 `http://localhost:5000`

### 2. 注册账户
- 点击"立即注册"
- 填写学生信息（用户名、学号、邮箱等）
- 提交注册

### 3. 登录系统
- 使用注册的用户名和密码登录
- 进入个人仪表板

### 4. 开始分析
- 点击"情绪分析"菜单
- 输入要分析的文本
- 查看分析结果

## 管理员功能

### 默认管理员账户
- **用户名**: admin
- **密码**: 需要在数据库中设置

### 管理员功能
- 用户管理：查看、激活/禁用用户
- 数据管理：查看所有分析记录
- 系统统计：全局数据统计和分析

## 常见问题

### Q1: 模型加载失败
**问题**: 启动时提示模型文件不存在
**解决**: 确保 `nlp_structbert_emotion-classification_chinese-base` 目录存在且包含所有必要文件

### Q2: 数据库连接失败
**问题**: 无法连接到MySQL数据库
**解决**: 
1. 检查MySQL服务是否启动
2. 验证 `.env` 文件中的数据库配置
3. 确认数据库用户权限

### Q3: 依赖安装失败
**问题**: pip安装依赖时出错
**解决**:
1. 升级pip: `python -m pip install --upgrade pip`
2. 使用国内镜像: `pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/`

### Q4: 端口被占用
**问题**: 5000端口已被占用
**解决**: 修改 `run.py` 中的端口号，或停止占用5000端口的程序

## 开发模式

### 启用调试模式
```bash
export FLASK_ENV=development  # Linux/macOS
set FLASK_ENV=development     # Windows
python run.py
```

### 查看日志
日志文件位置：`logs/app.log`

### 数据库迁移
如果修改了数据模型，需要重新创建数据库表：
```python
from app import create_app, db
app = create_app()
with app.app_context():
    db.create_all()
```

## 生产部署

### 使用Gunicorn
```bash
pip install gunicorn
gunicorn -w 4 -b 0.0.0.0:5000 run:app
```

### 使用Nginx反向代理
```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    location / {
        proxy_pass http://127.0.0.1:5000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

## 系统维护

### 数据备份
```bash
# 备份数据库
mysqldump -u root -p emotion_analysis > backup_$(date +%Y%m%d).sql

# 备份上传文件
tar -czf uploads_backup_$(date +%Y%m%d).tar.gz uploads/
```

### 日志清理
```bash
# 清理旧日志文件
find logs/ -name "*.log" -mtime +30 -delete
```

### 性能监控
- 监控数据库连接数
- 检查内存使用情况
- 观察响应时间

## 技术支持

如果遇到问题，请：
1. 查看日志文件 `logs/app.log`
2. 检查系统要求是否满足
3. 参考常见问题解答
4. 联系技术支持

## 更新日志

### v1.0.0 (2024-11-12)
- 初始版本发布
- 基础情绪分析功能
- 用户管理系统
- 数据统计功能
- 管理员后台

---

**注意**: 本系统仅用于学术研究和教育目的，请遵守相关法律法规和学校规定。
