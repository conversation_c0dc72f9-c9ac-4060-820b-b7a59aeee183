{% extends "base.html" %}

{% block title %}情绪分析 - {{ SYSTEM_NAME }}{% endblock %}

{% block content %}
<div class="row">
    <div class="col-lg-8 mx-auto">
        <div class="card shadow">
            <div class="card-header bg-primary text-white">
                <h4 class="mb-0">
                    <i class="fas fa-search me-2"></i>文本情绪分析
                </h4>
            </div>
            <div class="card-body">
                <form method="POST" id="analyzeForm">
                    <div class="mb-3">
                        <label for="text" class="form-label">
                            <i class="fas fa-edit me-2"></i>请输入要分析的文本内容
                        </label>
                        <textarea class="form-control" id="text" name="text" rows="8" 
                                placeholder="请输入您想要分析情绪的文本内容，例如：&#10;- 日记内容&#10;- 心情感受&#10;- 学习体会&#10;- 生活感悟&#10;等等..." 
                                required maxlength="500"></textarea>
                        <div class="form-text">
                            <span id="charCount">0</span>/500 字符
                        </div>
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h6 class="card-title">
                                        <i class="fas fa-info-circle me-2"></i>分析说明
                                    </h6>
                                    <ul class="mb-0 small">
                                        <li>系统将识别7种基本情绪</li>
                                        <li>分析结果包含置信度评分</li>
                                        <li>支持中文文本分析</li>
                                        <li>分析记录将被保存</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h6 class="card-title">
                                        <i class="fas fa-palette me-2"></i>情绪类型
                                    </h6>
                                    <div class="d-flex flex-wrap">
                                        <span class="badge bg-warning me-1 mb-1">高兴</span>
                                        <span class="badge bg-primary me-1 mb-1">悲伤</span>
                                        <span class="badge bg-danger me-1 mb-1">愤怒</span>
                                        <span class="badge bg-dark me-1 mb-1">恐惧</span>
                                        <span class="badge bg-success me-1 mb-1">厌恶</span>
                                        <span class="badge bg-info me-1 mb-1">喜好</span>
                                        <span class="badge bg-secondary me-1 mb-1">惊讶</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="d-grid gap-2 d-md-flex justify-content-md-between">
                        <div>
                            <button type="button" class="btn btn-outline-secondary me-2" onclick="clearText()">
                                <i class="fas fa-eraser me-2"></i>清空
                            </button>
                            <button type="button" class="btn btn-outline-info" onclick="loadExample()">
                                <i class="fas fa-lightbulb me-2"></i>示例文本
                            </button>
                        </div>
                        <div>
                            <button type="submit" class="btn btn-primary btn-lg" id="analyzeBtn">
                                <i class="fas fa-search me-2"></i>开始分析
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- 使用提示 -->
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-question-circle me-2"></i>使用提示
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6><i class="fas fa-check-circle text-success me-2"></i>适合分析的文本</h6>
                        <ul class="small">
                            <li>个人日记和心情记录</li>
                            <li>学习感悟和体会</li>
                            <li>生活感受和想法</li>
                            <li>对事件的情感表达</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6><i class="fas fa-exclamation-triangle text-warning me-2"></i>注意事项</h6>
                        <ul class="small">
                            <li>文本长度不超过500字符</li>
                            <li>建议使用完整的句子</li>
                            <li>避免过于简短的词语</li>
                            <li>每日分析次数有限制</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// 字符计数
const textArea = document.getElementById('text');
const charCount = document.getElementById('charCount');

textArea.addEventListener('input', function() {
    const count = this.value.length;
    charCount.textContent = count;
    
    if (count > 500) {
        charCount.classList.add('text-danger');
    } else {
        charCount.classList.remove('text-danger');
    }
});

// 清空文本
function clearText() {
    textArea.value = '';
    charCount.textContent = '0';
    charCount.classList.remove('text-danger');
}

// 加载示例文本
function loadExample() {
    const examples = [
        '今天的考试结果出来了，我竟然考了全班第一名！真的太开心了，所有的努力都没有白费。',
        '最近学习压力很大，感觉自己快要撑不住了，每天都很焦虑，不知道该怎么办。',
        '室友又在宿舍里大声说话，完全不考虑别人的感受，真的让我很生气。',
        '明天就要面试了，心里特别紧张，担心自己表现不好，希望一切顺利。',
        '看到食堂的饭菜质量越来越差，价格却越来越贵，真的很反感这种做法。',
        '今天在图书馆遇到了很久没见的老朋友，我们聊了很多，感觉特别温暖。',
        '刚才在路上看到一个小朋友摔倒了，旁边的人都没有去帮忙，这让我很震惊。'
    ];
    
    const randomExample = examples[Math.floor(Math.random() * examples.length)];
    textArea.value = randomExample;
    charCount.textContent = randomExample.length;
}

// 表单提交处理
document.getElementById('analyzeForm').addEventListener('submit', function(e) {
    const btn = document.getElementById('analyzeBtn');
    btn.disabled = true;
    btn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>分析中...';
});
</script>
{% endblock %}
