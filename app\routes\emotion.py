"""
情绪分析路由
"""
from flask import Blueprint, render_template, request, jsonify, flash, redirect, url_for
from flask_login import login_required, current_user
from datetime import datetime, timedelta
from app.services.emotion_service import emotion_service
from app.models.emotion_record import EmotionRecord

emotion_bp = Blueprint('emotion', __name__)

@emotion_bp.route('/analyze', methods=['GET', 'POST'])
@login_required
def analyze():
    """情绪分析页面"""
    if request.method == 'POST':
        text = request.form.get('text', '').strip()
        
        if not text:
            flash('请输入要分析的文本', 'error')
            return render_template('emotion/analyze.html')
        
        try:
            # 检查每日分析次数限制
            from flask import current_app
            daily_limit = current_app.config.get('DAILY_ANALYSIS_LIMIT', 100)
            today_count = current_user.get_emotion_count(days=1)
            
            if today_count >= daily_limit:
                flash(f'今日分析次数已达上限({daily_limit}次)', 'error')
                return render_template('emotion/analyze.html')
            
            # 执行情绪分析
            record = emotion_service.analyze_and_save(
                text=text,
                user_id=current_user.id,
                ip_address=request.remote_addr,
                user_agent=request.headers.get('User-Agent')
            )
            
            flash('分析完成！', 'success')
            return redirect(url_for('emotion.result', record_id=record.id))
            
        except Exception as e:
            flash(f'分析失败: {str(e)}', 'error')
    
    return render_template('emotion/analyze.html')

@emotion_bp.route('/api/analyze', methods=['POST'])
@login_required
def api_analyze():
    """API接口：情绪分析"""
    try:
        data = request.get_json()
        if not data or 'text' not in data:
            return jsonify({'error': '缺少文本参数'}), 400
        
        text = data['text'].strip()
        if not text:
            return jsonify({'error': '文本不能为空'}), 400
        
        # 检查每日分析次数限制
        from flask import current_app
        daily_limit = current_app.config.get('DAILY_ANALYSIS_LIMIT', 100)
        today_count = current_user.get_emotion_count(days=1)
        
        if today_count >= daily_limit:
            return jsonify({'error': f'今日分析次数已达上限({daily_limit}次)'}), 429
        
        # 执行情绪分析
        record = emotion_service.analyze_and_save(
            text=text,
            user_id=current_user.id,
            ip_address=request.remote_addr,
            user_agent=request.headers.get('User-Agent')
        )
        
        return jsonify({
            'success': True,
            'record_id': record.id,
            'result': record.get_formatted_result()
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@emotion_bp.route('/result/<int:record_id>')
@login_required
def result(record_id):
    """显示分析结果"""
    record = EmotionRecord.query.filter_by(
        id=record_id, 
        user_id=current_user.id
    ).first_or_404()
    
    return render_template('emotion/result.html', record=record)

@emotion_bp.route('/history')
@login_required
def history():
    """分析历史"""
    page = request.args.get('page', 1, type=int)
    per_page = 20
    
    records = EmotionRecord.query.filter_by(user_id=current_user.id)\
        .order_by(EmotionRecord.analysis_time.desc())\
        .paginate(page=page, per_page=per_page, error_out=False)
    
    return render_template('emotion/history.html', records=records)

@emotion_bp.route('/statistics')
@login_required
def statistics():
    """情绪统计"""
    days = request.args.get('days', 30, type=int)
    
    # 获取情绪分布
    emotion_stats = current_user.get_dominant_emotions(days=days)
    
    # 获取趋势数据
    trend_data = EmotionRecord.get_emotion_trend(
        user_id=current_user.id, 
        days=days
    )
    
    # 获取总分析次数
    total_count = current_user.get_emotion_count(days=days)
    
    return render_template('emotion/statistics.html',
                         emotion_stats=emotion_stats,
                         trend_data=trend_data,
                         total_count=total_count,
                         days=days)

@emotion_bp.route('/api/statistics')
@login_required
def api_statistics():
    """API接口：获取统计数据"""
    days = request.args.get('days', 30, type=int)
    
    try:
        # 获取情绪分布
        emotion_stats = current_user.get_dominant_emotions(days=days)
        
        # 获取趋势数据
        trend_data = EmotionRecord.get_emotion_trend(
            user_id=current_user.id, 
            days=days
        )
        
        # 获取总分析次数
        total_count = current_user.get_emotion_count(days=days)
        
        return jsonify({
            'emotion_distribution': emotion_stats,
            'trend_data': trend_data,
            'total_count': total_count,
            'days': days
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@emotion_bp.route('/batch', methods=['GET', 'POST'])
@login_required
def batch_analyze():
    """批量分析"""
    if request.method == 'POST':
        texts = request.form.get('texts', '').strip().split('\n')
        texts = [text.strip() for text in texts if text.strip()]
        
        if not texts:
            flash('请输入要分析的文本', 'error')
            return render_template('emotion/batch.html')
        
        if len(texts) > 10:
            flash('批量分析最多支持10条文本', 'error')
            return render_template('emotion/batch.html')
        
        try:
            # 检查每日分析次数限制
            from flask import current_app
            daily_limit = current_app.config.get('DAILY_ANALYSIS_LIMIT', 100)
            today_count = current_user.get_emotion_count(days=1)
            
            if today_count + len(texts) > daily_limit:
                flash(f'分析次数将超过每日限制({daily_limit}次)', 'error')
                return render_template('emotion/batch.html')
            
            # 批量分析
            results = []
            for text in texts:
                record = emotion_service.analyze_and_save(
                    text=text,
                    user_id=current_user.id,
                    ip_address=request.remote_addr,
                    user_agent=request.headers.get('User-Agent')
                )
                results.append(record)
            
            flash(f'批量分析完成！共分析{len(results)}条文本', 'success')
            return render_template('emotion/batch_result.html', results=results)
            
        except Exception as e:
            flash(f'批量分析失败: {str(e)}', 'error')
    
    return render_template('emotion/batch.html')

@emotion_bp.route('/delete/<int:record_id>', methods=['POST'])
@login_required
def delete_record(record_id):
    """删除分析记录"""
    record = EmotionRecord.query.filter_by(
        id=record_id, 
        user_id=current_user.id
    ).first_or_404()
    
    try:
        from app import db
        db.session.delete(record)
        db.session.commit()
        flash('记录已删除', 'success')
    except Exception as e:
        db.session.rollback()
        flash('删除失败', 'error')
    
    return redirect(url_for('emotion.history'))
