-- 测试数据插入脚本
-- 注意：密码未加密，仅用于开发测试

-- 清空现有数据
DELETE FROM emotion_records;
DELETE FROM users;

-- 插入管理员账户
INSERT INTO users (username, password_hash, email, real_name, role, is_active, created_at) VALUES
('admin', 'admin123', '<EMAIL>', '系统管理员', 'admin', 1, NOW());

-- 插入教师账户
INSERT INTO users (username, password_hash, email, student_id, real_name, department, major, role, is_active, created_at) VALUES
('teacher1', 'teacher123', '<EMAIL>', 'T001', '张教授', '计算机科学学院', '计算机科学与技术', 'teacher', 1, NOW()),
('teacher2', 'teacher123', '<EMAIL>', 'T002', '李老师', '数学与统计学院', '数学与应用数学', 'teacher', 1, NOW()),
('teacher3', 'teacher123', '<EMAIL>', 'T003', '王老师', '文学与传媒学院', '汉语言文学', 'teacher', 1, NOW());

-- 插入学生账户
INSERT INTO users (username, password_hash, email, student_id, real_name, department, major, grade, role, is_active, created_at) VALUES
('student1', 'student123', '<EMAIL>', '2021001', '陈小明', '计算机科学学院', '计算机科学与技术', '2021级', 'student', 1, NOW()),
('student2', 'student123', '<EMAIL>', '2021002', '李小红', '数学与统计学院', '数学与应用数学', '2021级', 'student', 1, NOW()),
('student3', 'student123', '<EMAIL>', '2022001', '王小华', '文学与传媒学院', '汉语言文学', '2022级', 'student', 1, NOW()),
('student4', 'student123', '<EMAIL>', '2022002', '张小丽', '教育科学学院', '学前教育', '2022级', 'student', 1, NOW()),
('student5', 'student123', '<EMAIL>', '2023001', '刘小强', '经济与管理学院', '工商管理', '2023级', 'student', 1, NOW()),
('student6', 'student123', '<EMAIL>', '2023002', '赵小美', '外国语学院', '英语', '2023级', 'student', 1, NOW()),
('student7', 'student123', '<EMAIL>', '2024001', '孙小亮', '体育学院', '体育教育', '2024级', 'student', 1, NOW()),
('student8', 'student123', '<EMAIL>', '2024002', '周小芳', '音乐舞蹈学院', '音乐学', '2024级', 'student', 1, NOW());

-- 插入测试分析记录
-- 为student1插入记录
INSERT INTO emotion_records (user_id, text_content, emotion_result, dominant_emotion, confidence_score, analysis_time, ip_address, user_agent) VALUES
(5, '今天考试考得很好，我很开心！', '{"text": "今天考试考得很好，我很开心！", "scores": {"恐惧": 0.05, "愤怒": 0.03, "厌恶": 0.02, "喜好": 0.15, "悲伤": 0.03, "高兴": 0.92, "惊讶": 0.08}, "dominant_emotion": "高兴", "confidence": 0.92}', '高兴', 0.92, DATE_SUB(NOW(), INTERVAL 1 DAY), '127.0.0.1', 'Test Data'),
(5, '最近学习压力很大，感觉很焦虑。', '{"text": "最近学习压力很大，感觉很焦虑。", "scores": {"恐惧": 0.85, "愤怒": 0.05, "厌恶": 0.03, "喜好": 0.02, "悲伤": 0.12, "高兴": 0.02, "惊讶": 0.06}, "dominant_emotion": "恐惧", "confidence": 0.85}', '恐惧', 0.85, DATE_SUB(NOW(), INTERVAL 2 DAY), '127.0.0.1', 'Test Data'),
(5, '室友总是很吵，让我很生气。', '{"text": "室友总是很吵，让我很生气。", "scores": {"恐惧": 0.08, "愤怒": 0.88, "厌恶": 0.15, "喜好": 0.02, "悲伤": 0.05, "高兴": 0.01, "惊讶": 0.03}, "dominant_emotion": "愤怒", "confidence": 0.88}', '愤怒', 0.88, DATE_SUB(NOW(), INTERVAL 3 DAY), '127.0.0.1', 'Test Data');

-- 为student2插入记录
INSERT INTO emotion_records (user_id, text_content, emotion_result, dominant_emotion, confidence_score, analysis_time, ip_address, user_agent) VALUES
(6, '明天要面试了，有点紧张害怕。', '{"text": "明天要面试了，有点紧张害怕。", "scores": {"恐惧": 0.79, "愤怒": 0.03, "厌恶": 0.02, "喜好": 0.05, "悲伤": 0.15, "高兴": 0.08, "惊讶": 0.12}, "dominant_emotion": "恐惧", "confidence": 0.79}', '恐惧', 0.79, DATE_SUB(NOW(), INTERVAL 1 DAY), '127.0.0.1', 'Test Data'),
(6, '食堂的饭菜质量越来越差了，真恶心。', '{"text": "食堂的饭菜质量越来越差了，真恶心。", "scores": {"恐惧": 0.05, "愤怒": 0.12, "厌恶": 0.83, "喜好": 0.01, "悲伤": 0.08, "高兴": 0.01, "惊讶": 0.03}, "dominant_emotion": "厌恶", "confidence": 0.83}', '厌恶', 0.83, DATE_SUB(NOW(), INTERVAL 2 DAY), '127.0.0.1', 'Test Data'),
(6, '我很喜欢这门课程，老师讲得很好。', '{"text": "我很喜欢这门课程，老师讲得很好。", "scores": {"恐惧": 0.02, "愤怒": 0.01, "厌恶": 0.01, "喜好": 0.90, "悲伤": 0.02, "高兴": 0.25, "惊讶": 0.05}, "dominant_emotion": "喜好", "confidence": 0.90}', '喜好', 0.90, DATE_SUB(NOW(), INTERVAL 4 DAY), '127.0.0.1', 'Test Data');

-- 为student3插入记录
INSERT INTO emotion_records (user_id, text_content, emotion_result, dominant_emotion, confidence_score, analysis_time, ip_address, user_agent) VALUES
(7, '刚才发生的事情让我很震惊！', '{"text": "刚才发生的事情让我很震惊！", "scores": {"恐惧": 0.15, "愤怒": 0.08, "厌恶": 0.05, "喜好": 0.03, "悲伤": 0.05, "高兴": 0.02, "惊讶": 0.87}, "dominant_emotion": "惊讶", "confidence": 0.87}', '惊讶', 0.87, DATE_SUB(NOW(), INTERVAL 1 HOUR), '127.0.0.1', 'Test Data'),
(7, '今天天气很好，心情不错。', '{"text": "今天天气很好，心情不错。", "scores": {"恐惧": 0.02, "愤怒": 0.01, "厌恶": 0.01, "喜好": 0.15, "悲伤": 0.02, "高兴": 0.78, "惊讶": 0.03}, "dominant_emotion": "高兴", "confidence": 0.78}', '高兴', 0.78, DATE_SUB(NOW(), INTERVAL 1 DAY), '127.0.0.1', 'Test Data'),
(7, '作业太多了，压力山大。', '{"text": "作业太多了，压力山大。", "scores": {"恐惧": 0.25, "愤怒": 0.08, "厌恶": 0.05, "喜好": 0.02, "悲伤": 0.82, "高兴": 0.01, "惊讶": 0.03}, "dominant_emotion": "悲伤", "confidence": 0.82}', '悲伤', 0.82, DATE_SUB(NOW(), INTERVAL 3 DAY), '127.0.0.1', 'Test Data');

-- 为student4插入记录
INSERT INTO emotion_records (user_id, text_content, emotion_result, dominant_emotion, confidence_score, analysis_time, ip_address, user_agent) VALUES
(8, '和朋友一起吃饭很开心。', '{"text": "和朋友一起吃饭很开心。", "scores": {"恐惧": 0.02, "愤怒": 0.01, "厌恶": 0.01, "喜好": 0.18, "悲伤": 0.02, "高兴": 0.85, "惊讶": 0.05}, "dominant_emotion": "高兴", "confidence": 0.85}', '高兴', 0.85, DATE_SUB(NOW(), INTERVAL 2 HOUR), '127.0.0.1', 'Test Data'),
(8, '期末考试快到了，好紧张啊。', '{"text": "期末考试快到了，好紧张啊。", "scores": {"恐惧": 0.76, "愤怒": 0.03, "厌恶": 0.02, "喜好": 0.05, "悲伤": 0.18, "高兴": 0.03, "惊讶": 0.08}, "dominant_emotion": "恐惧", "confidence": 0.76}', '恐惧', 0.76, DATE_SUB(NOW(), INTERVAL 1 DAY), '127.0.0.1', 'Test Data');

-- 为student5插入记录
INSERT INTO emotion_records (user_id, text_content, emotion_result, dominant_emotion, confidence_score, analysis_time, ip_address, user_agent) VALUES
(9, '这个电影真的太好看了！', '{"text": "这个电影真的太好看了！", "scores": {"恐惧": 0.02, "愤怒": 0.01, "厌恶": 0.01, "喜好": 0.22, "悲伤": 0.01, "高兴": 0.89, "惊讶": 0.15}, "dominant_emotion": "高兴", "confidence": 0.89}', '高兴', 0.89, DATE_SUB(NOW(), INTERVAL 3 HOUR), '127.0.0.1', 'Test Data'),
(9, '宿舍网络又断了，真是烦人。', '{"text": "宿舍网络又断了，真是烦人。", "scores": {"恐惧": 0.05, "愤怒": 0.81, "厌恶": 0.12, "喜好": 0.01, "悲伤": 0.08, "高兴": 0.01, "惊讶": 0.02}, "dominant_emotion": "愤怒", "confidence": 0.81}', '愤怒', 0.81, DATE_SUB(NOW(), INTERVAL 2 DAY), '127.0.0.1', 'Test Data');

-- 为student6插入记录
INSERT INTO emotion_records (user_id, text_content, emotion_result, dominant_emotion, confidence_score, analysis_time, ip_address, user_agent) VALUES
(10, '今天收到了心仪公司的面试通知。', '{"text": "今天收到了心仪公司的面试通知。", "scores": {"恐惧": 0.03, "愤怒": 0.01, "厌恶": 0.01, "喜好": 0.25, "悲伤": 0.01, "高兴": 0.93, "惊讶": 0.18}, "dominant_emotion": "高兴", "confidence": 0.93}', '高兴', 0.93, DATE_SUB(NOW(), INTERVAL 4 HOUR), '127.0.0.1', 'Test Data'),
(10, '感觉自己最近状态不太好。', '{"text": "感觉自己最近状态不太好。", "scores": {"恐惧": 0.15, "愤怒": 0.05, "厌恶": 0.08, "喜好": 0.02, "悲伤": 0.77, "高兴": 0.02, "惊讶": 0.03}, "dominant_emotion": "悲伤", "confidence": 0.77}', '悲伤', 0.77, DATE_SUB(NOW(), INTERVAL 1 DAY), '127.0.0.1', 'Test Data');

-- 为student7插入记录
INSERT INTO emotion_records (user_id, text_content, emotion_result, dominant_emotion, confidence_score, analysis_time, ip_address, user_agent) VALUES
(11, '这道题目真的很有趣。', '{"text": "这道题目真的很有趣。", "scores": {"恐惧": 0.02, "愤怒": 0.01, "厌恶": 0.01, "喜好": 0.84, "悲伤": 0.02, "高兴": 0.18, "惊讶": 0.12}, "dominant_emotion": "喜好", "confidence": 0.84}', '喜好', 0.84, DATE_SUB(NOW(), INTERVAL 5 HOUR), '127.0.0.1', 'Test Data'),
(11, '突然下雨了，没带伞。', '{"text": "突然下雨了，没带伞。", "scores": {"恐惧": 0.08, "愤怒": 0.05, "厌恶": 0.03, "喜好": 0.02, "悲伤": 0.72, "高兴": 0.02, "惊讶": 0.15}, "dominant_emotion": "悲伤", "confidence": 0.72}', '悲伤', 0.72, DATE_SUB(NOW(), INTERVAL 2 DAY), '127.0.0.1', 'Test Data');

-- 为student8插入记录
INSERT INTO emotion_records (user_id, text_content, emotion_result, dominant_emotion, confidence_score, analysis_time, ip_address, user_agent) VALUES
(12, '老师今天表扬了我的作业。', '{"text": "老师今天表扬了我的作业。", "scores": {"恐惧": 0.02, "愤怒": 0.01, "厌恶": 0.01, "喜好": 0.22, "悲伤": 0.01, "高兴": 0.91, "惊讶": 0.08}, "dominant_emotion": "高兴", "confidence": 0.91}', '高兴', 0.91, DATE_SUB(NOW(), INTERVAL 6 HOUR), '127.0.0.1', 'Test Data'),
(12, '图书馆里太吵了，无法专心学习。', '{"text": "图书馆里太吵了，无法专心学习。", "scores": {"恐惧": 0.05, "愤怒": 0.79, "厌恶": 0.08, "喜好": 0.01, "悲伤": 0.12, "高兴": 0.01, "惊讶": 0.02}, "dominant_emotion": "愤怒", "confidence": 0.79}', '愤怒', 0.79, DATE_SUB(NOW(), INTERVAL 1 DAY), '127.0.0.1', 'Test Data'),
(12, '看到这个新闻真的很震惊。', '{"text": "看到这个新闻真的很震惊。", "scores": {"恐惧": 0.12, "愤怒": 0.05, "厌恶": 0.03, "喜好": 0.02, "悲伤": 0.08, "高兴": 0.02, "惊讶": 0.86}, "dominant_emotion": "惊讶", "confidence": 0.86}', '惊讶', 0.86, DATE_SUB(NOW(), INTERVAL 3 DAY), '127.0.0.1', 'Test Data');

-- 显示插入结果
SELECT '用户数据插入完成' as message;
SELECT COUNT(*) as user_count FROM users;
SELECT COUNT(*) as record_count FROM emotion_records;

-- 显示账户信息
SELECT '=== 测试账户信息 ===' as info;
SELECT username, password_hash as password, role, real_name FROM users ORDER BY role, username;
