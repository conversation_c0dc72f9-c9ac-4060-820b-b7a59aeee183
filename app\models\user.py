"""
用户数据模型
"""
from datetime import datetime
from flask_sqlalchemy import SQLAlchemy
from flask_login import UserMixin
from werkzeug.security import generate_password_hash, check_password_hash
from app import db

class User(UserMixin, db.Model):
    """用户模型"""
    __tablename__ = 'users'
    
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(50), unique=True, nullable=False, comment='用户名')
    password_hash = db.Column(db.String(255), nullable=False, comment='密码哈希')
    email = db.Column(db.String(100), unique=True, comment='邮箱')
    student_id = db.Column(db.String(20), unique=True, comment='学号')
    real_name = db.Column(db.String(50), comment='真实姓名')
    department = db.Column(db.String(100), comment='院系')
    major = db.Column(db.String(100), comment='专业')
    grade = db.Column(db.String(10), comment='年级')
    role = db.Column(db.Enum('student', 'teacher', 'admin'), default='student', comment='用户角色')
    is_active = db.Column(db.Boolean, default=True, comment='是否激活')
    created_at = db.Column(db.DateTime, default=datetime.utcnow, comment='创建时间')
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment='更新时间')
    
    # 关系
    emotion_records = db.relationship('EmotionRecord', backref='user', lazy='dynamic', cascade='all, delete-orphan')
    emotion_statistics = db.relationship('EmotionStatistic', backref='user', lazy='dynamic', cascade='all, delete-orphan')
    
    def __init__(self, **kwargs):
        super(User, self).__init__(**kwargs)
    
    def set_password(self, password):
        """设置密码"""
        self.password_hash = generate_password_hash(password)
    
    def check_password(self, password):
        """验证密码"""
        return check_password_hash(self.password_hash, password)
    
    def is_admin(self):
        """检查是否为管理员"""
        return self.role == 'admin'
    
    def is_teacher(self):
        """检查是否为教师"""
        return self.role == 'teacher'
    
    def is_student(self):
        """检查是否为学生"""
        return self.role == 'student'
    
    def get_emotion_count(self, days=30):
        """获取最近N天的情绪分析次数"""
        from datetime import datetime, timedelta
        from app.models.emotion_record import EmotionRecord
        
        start_date = datetime.utcnow() - timedelta(days=days)
        return self.emotion_records.filter(
            EmotionRecord.analysis_time >= start_date
        ).count()
    
    def get_dominant_emotions(self, days=30):
        """获取最近N天的主要情绪分布"""
        from datetime import datetime, timedelta
        from app.models.emotion_record import EmotionRecord
        from sqlalchemy import func
        
        start_date = datetime.utcnow() - timedelta(days=days)
        
        results = db.session.query(
            EmotionRecord.dominant_emotion,
            func.count(EmotionRecord.id).label('count')
        ).filter(
            EmotionRecord.user_id == self.id,
            EmotionRecord.analysis_time >= start_date
        ).group_by(EmotionRecord.dominant_emotion).all()
        
        return {emotion: count for emotion, count in results}
    
    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'username': self.username,
            'email': self.email,
            'student_id': self.student_id,
            'real_name': self.real_name,
            'department': self.department,
            'major': self.major,
            'grade': self.grade,
            'role': self.role,
            'is_active': self.is_active,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }
    
    def __repr__(self):
        return f'<User {self.username}>'
