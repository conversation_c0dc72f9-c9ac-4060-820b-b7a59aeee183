{% extends "base.html" %}

{% block title %}个人资料 - {{ SYSTEM_NAME }}{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-8 mx-auto">
        <div class="card shadow">
            <div class="card-header bg-info text-white">
                <h4 class="mb-0">
                    <i class="fas fa-user-circle me-2"></i>个人资料
                </h4>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4 text-center mb-4">
                        <div class="profile-avatar">
                            <i class="fas fa-user-circle fa-5x text-muted mb-3"></i>
                            <h5>{{ user.real_name or user.username }}</h5>
                            <span class="badge bg-{{ 'danger' if user.role == 'admin' else 'primary' if user.role == 'teacher' else 'success' }}">
                                {{ '管理员' if user.role == 'admin' else '教师' if user.role == 'teacher' else '学生' }}
                            </span>
                        </div>
                    </div>
                    <div class="col-md-8">
                        <table class="table table-borderless">
                            <tr>
                                <th width="30%">用户名:</th>
                                <td>{{ user.username }}</td>
                            </tr>
                            <tr>
                                <th>真实姓名:</th>
                                <td>{{ user.real_name or '-' }}</td>
                            </tr>
                            <tr>
                                <th>邮箱:</th>
                                <td>{{ user.email or '-' }}</td>
                            </tr>
                            {% if user.role == 'student' %}
                            <tr>
                                <th>学号:</th>
                                <td>{{ user.student_id or '-' }}</td>
                            </tr>
                            {% endif %}
                            <tr>
                                <th>院系:</th>
                                <td>{{ user.department or '-' }}</td>
                            </tr>
                            <tr>
                                <th>专业:</th>
                                <td>{{ user.major or '-' }}</td>
                            </tr>
                            {% if user.role == 'student' %}
                            <tr>
                                <th>年级:</th>
                                <td>{{ user.grade or '-' }}</td>
                            </tr>
                            {% endif %}
                            <tr>
                                <th>注册时间:</th>
                                <td>{{ user.created_at.strftime('%Y-%m-%d %H:%M') if user.created_at else '-' }}</td>
                            </tr>
                            <tr>
                                <th>账户状态:</th>
                                <td>
                                    <span class="badge bg-{{ 'success' if user.is_active else 'danger' }}">
                                        {{ '正常' if user.is_active else '已禁用' }}
                                    </span>
                                </td>
                            </tr>
                        </table>
                    </div>
                </div>
                
                <hr>
                
                <div class="text-center">
                    <a href="{{ url_for('auth.edit_profile') }}" class="btn btn-primary me-2">
                        <i class="fas fa-edit me-2"></i>编辑资料
                    </a>
                    <a href="{{ url_for('auth.change_password') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-key me-2"></i>修改密码
                    </a>
                </div>
            </div>
        </div>
        
        <!-- 使用统计 -->
        {% if user.role in ['student', 'teacher'] %}
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-bar me-2"></i>使用统计
                </h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-md-3">
                        <div class="stat-item">
                            <h4 class="text-primary">{{ user.get_emotion_count(days=1) }}</h4>
                            <p class="text-muted mb-0">今日分析</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-item">
                            <h4 class="text-success">{{ user.get_emotion_count(days=7) }}</h4>
                            <p class="text-muted mb-0">本周分析</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-item">
                            <h4 class="text-info">{{ user.get_emotion_count(days=30) }}</h4>
                            <p class="text-muted mb-0">本月分析</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-item">
                            <h4 class="text-warning">{{ user.get_emotion_count(days=365) }}</h4>
                            <p class="text-muted mb-0">总计分析</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.profile-avatar {
    padding: 20px;
}

.stat-item {
    padding: 15px;
    border-right: 1px solid #eee;
}

.stat-item:last-child {
    border-right: none;
}

@media (max-width: 768px) {
    .stat-item {
        border-right: none;
        border-bottom: 1px solid #eee;
        margin-bottom: 10px;
    }
    
    .stat-item:last-child {
        border-bottom: none;
        margin-bottom: 0;
    }
}
</style>
{% endblock %}
