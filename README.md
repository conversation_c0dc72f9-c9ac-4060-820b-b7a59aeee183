# 黔南民族师范学院学生情绪分析系统

## 项目简介
基于StructBERT模型的学生情绪分析系统，用于分析学生文本内容的情绪状态，帮助学校了解学生心理健康状况。

## 技术栈
- **AI模型**: StructBERT中文情绪分类模型
- **后端**: Python Flask
- **数据库**: MySQL
- **前端**: HTML/CSS/JavaScript (Bootstrap)
- **部署**: Docker (可选)

## 功能特性
1. **情绪分析**: 识别7种情绪（恐惧、愤怒、厌恶、喜好、悲伤、高兴、惊讶）
2. **用户管理**: 学生和管理员登录系统
3. **数据统计**: 情绪趋势分析和可视化
4. **历史记录**: 保存分析历史和结果导出
5. **实时分析**: 支持实时文本情绪分析

## 项目结构
```
emo2/
├── app/                          # Flask应用主目录
│   ├── __init__.py              # Flask应用初始化
│   ├── models/                  # 数据模型
│   │   ├── __init__.py
│   │   ├── user.py             # 用户模型
│   │   └── emotion_record.py   # 情绪记录模型
│   ├── routes/                  # 路由控制器
│   │   ├── __init__.py
│   │   ├── auth.py             # 认证路由
│   │   ├── emotion.py          # 情绪分析路由
│   │   └── admin.py            # 管理员路由
│   ├── services/                # 业务逻辑层
│   │   ├── __init__.py
│   │   ├── emotion_service.py  # 情绪分析服务
│   │   └── user_service.py     # 用户服务
│   ├── utils/                   # 工具函数
│   │   ├── __init__.py
│   │   └── database.py         # 数据库工具
│   └── templates/               # HTML模板
│       ├── base.html
│       ├── login.html
│       ├── dashboard.html
│       └── analysis.html
├── static/                      # 静态文件
│   ├── css/
│   ├── js/
│   └── images/
├── nlp_structbert_emotion-classification_chinese-base/  # BERT模型
├── config/                      # 配置文件
│   ├── config.py               # 应用配置
│   └── database.sql            # 数据库初始化脚本
├── tests/                       # 测试文件
├── requirements.txt             # Python依赖
├── run.py                      # 应用启动文件
└── README.md                   # 项目说明
```

## 快速启动 🚀

### 方法一：一键启动（推荐）
```bash
# 确保已安装MySQL并创建数据库
mysql -u root -p
CREATE DATABASE emotion_analysis;
exit

# 一键启动（自动安装依赖、初始化数据、启动服务）
python quick_start.py
```

### 方法二：手动安装
```bash
# 1. 安装依赖
pip install -r requirements.txt

# 2. 初始化测试数据
python init_test_data.py

# 3. 启动应用
python run.py
```

### 测试账户
- **管理员**: admin / admin123
- **教师**: teacher1, teacher2, teacher3 / teacher123
- **学生**: student1-student8 / student123

## 系统架构

### 情绪分类标签
- 恐惧 (Fear)
- 愤怒 (Anger) 
- 厌恶 (Disgust)
- 喜好 (Like)
- 悲伤 (Sadness)
- 高兴 (Joy)
- 惊讶 (Surprise)

### 数据流程
1. 用户输入文本
2. 文本预处理
3. StructBERT模型推理
4. 结果后处理和存储
5. 数据可视化展示

## 开发计划
- [x] 项目架构设计
- [ ] 数据库设计
- [ ] Flask后端开发
- [ ] BERT模型集成
- [ ] 前端界面开发
- [ ] 系统测试与优化

## 贡献指南
欢迎提交Issue和Pull Request来改进项目。

## 许可证
MIT License
