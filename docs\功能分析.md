# 黔南民族师范学院学生情绪分析系统功能分析

## 系统概述

本系统是基于StructBERT深度学习模型的学生情绪分析系统，旨在帮助学校了解学生的心理健康状况，为学生提供情绪分析和心理健康指导。

## 核心功能模块

### 1. 用户管理模块

#### 1.1 用户注册
- **功能描述**: 学生用户注册账户
- **主要字段**: 用户名、密码、邮箱、学号、真实姓名、院系、专业、年级
- **验证规则**: 
  - 用户名唯一性检查
  - 邮箱格式验证和唯一性检查
  - 学号唯一性检查
  - 密码强度验证（至少6位）
- **业务逻辑**: 新用户默认为学生角色，需要激活状态

#### 1.2 用户登录
- **功能描述**: 用户身份验证和会话管理
- **支持功能**: 
  - 记住登录状态
  - 登录失败提示
  - 账户状态检查
- **安全措施**: 密码哈希存储，会话管理

#### 1.3 用户资料管理
- **功能描述**: 用户可以查看和编辑个人资料
- **可编辑字段**: 真实姓名、邮箱、院系、专业、年级
- **不可编辑字段**: 用户名、学号（保证数据一致性）

#### 1.4 密码管理
- **功能描述**: 用户可以修改登录密码
- **验证流程**: 当前密码验证 → 新密码确认 → 密码强度检查

### 2. 情绪分析模块

#### 2.1 文本情绪分析
- **核心功能**: 使用StructBERT模型分析文本情绪
- **支持情绪类型**: 
  - 高兴 (Joy)
  - 悲伤 (Sadness)
  - 愤怒 (Anger)
  - 恐惧 (Fear)
  - 厌恶 (Disgust)
  - 喜好 (Like)
  - 惊讶 (Surprise)
- **输入限制**: 最大500字符
- **输出结果**: 
  - 主要情绪类型
  - 各情绪的置信度分数
  - 分析时间戳

#### 2.2 批量分析
- **功能描述**: 支持一次性分析多条文本
- **限制条件**: 最多10条文本，总体不超过每日限额
- **结果展示**: 批量结果汇总和详细分析

#### 2.3 分析历史
- **功能描述**: 保存和查看历史分析记录
- **查询功能**: 
  - 按时间排序
  - 分页显示
  - 记录详情查看
- **操作功能**: 删除不需要的记录

#### 2.4 实时分析API
- **功能描述**: 提供RESTful API接口
- **请求格式**: JSON格式文本数据
- **响应格式**: 包含分析结果和元数据的JSON

### 3. 数据统计模块

#### 3.1 个人情绪统计
- **统计维度**: 
  - 情绪分布饼图
  - 时间趋势折线图
  - 分析次数统计
- **时间范围**: 支持7天、30天、90天等不同时间段
- **可视化**: 使用Chart.js实现交互式图表

#### 3.2 情绪趋势分析
- **功能描述**: 分析用户情绪变化趋势
- **数据来源**: 历史分析记录
- **展示方式**: 时间轴图表，显示主要情绪随时间的变化

#### 3.3 统计报告
- **个人报告**: 生成个人情绪分析报告
- **数据导出**: 支持导出分析数据（CSV格式）

### 4. 管理员模块

#### 4.1 系统仪表板
- **功能描述**: 管理员查看系统整体运行状况
- **统计数据**: 
  - 用户总数
  - 分析记录总数
  - 今日分析量
  - 活跃用户统计

#### 4.2 用户管理
- **功能描述**: 管理员管理所有用户账户
- **操作功能**: 
  - 查看用户列表
  - 搜索用户
  - 激活/禁用用户
  - 查看用户详细信息

#### 4.3 数据管理
- **功能描述**: 管理系统中的分析数据
- **操作功能**: 
  - 查看所有分析记录
  - 按条件筛选记录
  - 数据统计和分析

#### 4.4 系统统计
- **功能描述**: 全局数据统计和分析
- **统计内容**: 
  - 全校情绪分布
  - 各院系情绪对比
  - 时间趋势分析
  - 用户活跃度分析

## 技术特性

### 1. AI模型集成
- **模型**: StructBERT中文情绪分类模型
- **准确率**: F1分数0.5743
- **处理能力**: 支持实时分析和批量处理
- **优化**: 模型缓存和GPU加速支持

### 2. 数据安全
- **密码安全**: 使用Werkzeug进行密码哈希
- **会话管理**: Flask-Login提供安全的会话管理
- **数据保护**: 用户数据加密存储
- **访问控制**: 基于角色的权限管理

### 3. 性能优化
- **数据库优化**: 
  - 索引优化
  - 查询优化
  - 连接池管理
- **缓存机制**: 模型缓存和查询结果缓存
- **异步处理**: 支持批量分析的异步处理

### 4. 用户体验
- **响应式设计**: 支持PC和移动端访问
- **交互式图表**: 使用Chart.js实现数据可视化
- **实时反馈**: Ajax异步请求提升用户体验
- **友好提示**: 完善的错误处理和用户提示

## 系统限制

### 1. 使用限制
- **文本长度**: 单次分析最大500字符
- **每日限额**: 每用户每日最多100次分析
- **批量限制**: 批量分析最多10条文本

### 2. 技术限制
- **语言支持**: 仅支持中文文本分析
- **模型固定**: 使用预训练模型，不支持在线学习
- **硬件要求**: 推荐使用GPU加速推理

## 扩展功能建议

### 1. 短期扩展
- **情绪日记**: 用户可以记录每日情绪日记
- **情绪提醒**: 基于分析结果的情绪健康提醒
- **数据导出**: 支持更多格式的数据导出

### 2. 长期扩展
- **多模态分析**: 支持语音和图像情绪分析
- **个性化建议**: 基于情绪分析的个性化心理健康建议
- **社交功能**: 匿名情绪分享和交流平台
- **专业咨询**: 集成心理咨询师在线服务

## 部署建议

### 1. 开发环境
- **Python**: 3.8+
- **数据库**: MySQL 8.0+
- **Web服务器**: Flask开发服务器

### 2. 生产环境
- **Web服务器**: Nginx + Gunicorn
- **数据库**: MySQL主从复制
- **缓存**: Redis
- **监控**: 日志监控和性能监控

### 3. 安全建议
- **HTTPS**: 使用SSL证书
- **防火墙**: 配置适当的防火墙规则
- **备份**: 定期数据备份
- **更新**: 及时更新依赖包和系统补丁
