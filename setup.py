"""
黔南民族师范学院学生情绪分析系统安装脚本
"""
import os
import sys
import subprocess

def install_requirements():
    """安装Python依赖"""
    print("正在安装Python依赖...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✓ Python依赖安装完成")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ Python依赖安装失败: {e}")
        return False

def create_directories():
    """创建必要的目录"""
    print("正在创建目录结构...")
    directories = [
        'logs',
        'uploads',
        'static/css',
        'static/js',
        'static/images',
        'app/templates/errors',
        'app/templates/admin'
    ]
    
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
        print(f"✓ 创建目录: {directory}")
    
    return True

def create_env_file():
    """创建环境配置文件"""
    print("正在创建环境配置文件...")
    
    env_content = """# 黔南民族师范学院学生情绪分析系统环境配置

# Flask配置
FLASK_ENV=development
SECRET_KEY=qnnu-emotion-analysis-secret-key-2024

# 数据库配置
MYSQL_HOST=localhost
MYSQL_PORT=3306
MYSQL_USER=root
MYSQL_PASSWORD=password
MYSQL_DATABASE=emotion_analysis

# 其他配置
MAX_TEXT_LENGTH=500
DAILY_ANALYSIS_LIMIT=100
"""
    
    with open('.env', 'w', encoding='utf-8') as f:
        f.write(env_content)
    
    print("✓ 环境配置文件创建完成")
    return True

def check_model():
    """检查模型文件"""
    print("正在检查模型文件...")
    
    model_path = 'nlp_structbert_emotion-classification_chinese-base'
    required_files = [
        'config.json',
        'pytorch_model.bin',
        'vocab.txt'
    ]
    
    if not os.path.exists(model_path):
        print(f"✗ 模型目录不存在: {model_path}")
        return False
    
    for file in required_files:
        file_path = os.path.join(model_path, file)
        if not os.path.exists(file_path):
            print(f"✗ 模型文件缺失: {file_path}")
            return False
        print(f"✓ 模型文件存在: {file}")
    
    print("✓ 模型文件检查完成")
    return True

def main():
    """主安装流程"""
    print("=" * 60)
    print("黔南民族师范学院学生情绪分析系统 - 安装程序")
    print("=" * 60)
    
    steps = [
        ("创建目录结构", create_directories),
        ("创建环境配置", create_env_file),
        ("检查模型文件", check_model),
        ("安装Python依赖", install_requirements)
    ]
    
    success_count = 0
    for step_name, step_func in steps:
        print(f"\n[{success_count + 1}/{len(steps)}] {step_name}")
        if step_func():
            success_count += 1
        else:
            print(f"✗ {step_name}失败，安装中断")
            break
    
    print("\n" + "=" * 60)
    if success_count == len(steps):
        print("✓ 系统安装完成！")
        print("\n下一步操作：")
        print("1. 配置MySQL数据库")
        print("2. 修改.env文件中的数据库连接信息")
        print("3. 运行: python run.py")
        print("4. 访问: http://localhost:5000")
    else:
        print(f"✗ 安装失败 ({success_count}/{len(steps)} 步骤完成)")
    print("=" * 60)

if __name__ == "__main__":
    main()
