"""
管理员路由
"""
from flask import Blueprint, render_template, request, jsonify, flash, redirect, url_for
from flask_login import login_required, current_user
from functools import wraps
from datetime import datetime, timedelta
from sqlalchemy import func
from app import db
from app.models.user import User
from app.models.emotion_record import EmotionRecord, EmotionStatistic

admin_bp = Blueprint('admin', __name__)

def admin_required(f):
    """管理员权限装饰器"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not current_user.is_authenticated or not current_user.is_admin():
            flash('需要管理员权限', 'error')
            return redirect(url_for('main.index'))
        return f(*args, **kwargs)
    return decorated_function

@admin_bp.route('/dashboard')
@login_required
@admin_required
def dashboard():
    """管理员仪表板"""
    # 获取系统统计数据
    total_users = User.query.filter_by(role='student').count()
    total_records = EmotionRecord.query.count()
    
    # 今日统计
    today = datetime.utcnow().date()
    today_records = EmotionRecord.query.filter(
        func.date(EmotionRecord.analysis_time) == today
    ).count()
    
    # 最近7天的分析趋势
    week_ago = datetime.utcnow() - timedelta(days=7)
    daily_stats = db.session.query(
        func.date(EmotionRecord.analysis_time).label('date'),
        func.count(EmotionRecord.id).label('count')
    ).filter(
        EmotionRecord.analysis_time >= week_ago
    ).group_by(
        func.date(EmotionRecord.analysis_time)
    ).all()
    
    # 情绪分布统计
    emotion_stats = db.session.query(
        EmotionRecord.dominant_emotion,
        func.count(EmotionRecord.id).label('count')
    ).group_by(EmotionRecord.dominant_emotion).all()
    
    # 活跃用户统计
    active_users = db.session.query(
        User.id,
        User.username,
        User.real_name,
        func.count(EmotionRecord.id).label('analysis_count')
    ).join(EmotionRecord).filter(
        EmotionRecord.analysis_time >= week_ago
    ).group_by(User.id).order_by(
        func.count(EmotionRecord.id).desc()
    ).limit(10).all()
    
    return render_template('admin/dashboard.html',
                         total_users=total_users,
                         total_records=total_records,
                         today_records=today_records,
                         daily_stats=daily_stats,
                         emotion_stats=emotion_stats,
                         active_users=active_users)

@admin_bp.route('/users')
@login_required
@admin_required
def users():
    """用户管理"""
    page = request.args.get('page', 1, type=int)
    search = request.args.get('search', '', type=str)
    
    query = User.query.filter_by(role='student')
    
    if search:
        query = query.filter(
            db.or_(
                User.username.contains(search),
                User.real_name.contains(search),
                User.student_id.contains(search),
                User.email.contains(search)
            )
        )
    
    users = query.order_by(User.created_at.desc()).paginate(
        page=page, per_page=20, error_out=False
    )
    
    return render_template('admin/users.html', users=users, search=search)

@admin_bp.route('/users/<int:user_id>')
@login_required
@admin_required
def user_detail(user_id):
    """用户详情"""
    user = User.query.get_or_404(user_id)
    
    # 获取用户分析记录
    records = EmotionRecord.query.filter_by(user_id=user_id)\
        .order_by(EmotionRecord.analysis_time.desc())\
        .limit(20).all()
    
    # 获取用户情绪统计
    emotion_stats = user.get_dominant_emotions(days=30)
    analysis_count = user.get_emotion_count(days=30)
    
    return render_template('admin/user_detail.html',
                         user=user,
                         records=records,
                         emotion_stats=emotion_stats,
                         analysis_count=analysis_count)

@admin_bp.route('/users/<int:user_id>/toggle-status', methods=['POST'])
@login_required
@admin_required
def toggle_user_status(user_id):
    """切换用户状态"""
    user = User.query.get_or_404(user_id)
    
    if user.role == 'admin':
        flash('不能修改管理员状态', 'error')
        return redirect(url_for('admin.users'))
    
    try:
        user.is_active = not user.is_active
        db.session.commit()
        
        status = '激活' if user.is_active else '禁用'
        flash(f'用户 {user.username} 已{status}', 'success')
    except Exception as e:
        db.session.rollback()
        flash('操作失败', 'error')
    
    return redirect(url_for('admin.users'))

@admin_bp.route('/records')
@login_required
@admin_required
def records():
    """分析记录管理"""
    page = request.args.get('page', 1, type=int)
    emotion = request.args.get('emotion', '', type=str)
    user_id = request.args.get('user_id', 0, type=int)
    
    query = EmotionRecord.query
    
    if emotion:
        query = query.filter_by(dominant_emotion=emotion)
    
    if user_id:
        query = query.filter_by(user_id=user_id)
    
    records = query.order_by(EmotionRecord.analysis_time.desc()).paginate(
        page=page, per_page=20, error_out=False
    )
    
    # 获取所有情绪类型用于筛选
    emotions = db.session.query(EmotionRecord.dominant_emotion.distinct()).all()
    emotions = [e[0] for e in emotions]
    
    return render_template('admin/records.html',
                         records=records,
                         emotions=emotions,
                         selected_emotion=emotion,
                         selected_user_id=user_id)

@admin_bp.route('/statistics')
@login_required
@admin_required
def statistics():
    """系统统计"""
    days = request.args.get('days', 30, type=int)
    
    # 获取全局情绪趋势
    trend_data = EmotionRecord.get_emotion_trend(days=days)
    
    # 获取情绪分布
    emotion_distribution = db.session.query(
        EmotionRecord.dominant_emotion,
        func.count(EmotionRecord.id).label('count')
    ).group_by(EmotionRecord.dominant_emotion).all()
    
    # 获取用户活跃度
    user_activity = db.session.query(
        User.department,
        func.count(EmotionRecord.id).label('count')
    ).join(EmotionRecord).filter(
        EmotionRecord.analysis_time >= datetime.utcnow() - timedelta(days=days)
    ).group_by(User.department).all()
    
    # 获取每日分析量
    daily_analysis = db.session.query(
        func.date(EmotionRecord.analysis_time).label('date'),
        func.count(EmotionRecord.id).label('count')
    ).filter(
        EmotionRecord.analysis_time >= datetime.utcnow() - timedelta(days=days)
    ).group_by(
        func.date(EmotionRecord.analysis_time)
    ).order_by('date').all()
    
    return render_template('admin/statistics.html',
                         trend_data=trend_data,
                         emotion_distribution=emotion_distribution,
                         user_activity=user_activity,
                         daily_analysis=daily_analysis,
                         days=days)

@admin_bp.route('/api/statistics')
@login_required
@admin_required
def api_statistics():
    """API接口：获取管理员统计数据"""
    days = request.args.get('days', 30, type=int)
    
    try:
        # 获取全局情绪趋势
        trend_data = EmotionRecord.get_emotion_trend(days=days)
        
        # 获取情绪分布
        emotion_distribution = db.session.query(
            EmotionRecord.dominant_emotion,
            func.count(EmotionRecord.id).label('count')
        ).group_by(EmotionRecord.dominant_emotion).all()
        
        emotion_dist_dict = {emotion: count for emotion, count in emotion_distribution}
        
        # 获取每日分析量
        daily_analysis = db.session.query(
            func.date(EmotionRecord.analysis_time).label('date'),
            func.count(EmotionRecord.id).label('count')
        ).filter(
            EmotionRecord.analysis_time >= datetime.utcnow() - timedelta(days=days)
        ).group_by(
            func.date(EmotionRecord.analysis_time)
        ).order_by('date').all()
        
        daily_dict = {date.strftime('%Y-%m-%d'): count for date, count in daily_analysis}
        
        return jsonify({
            'trend_data': trend_data,
            'emotion_distribution': emotion_dist_dict,
            'daily_analysis': daily_dict,
            'days': days
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@admin_bp.route('/export')
@login_required
@admin_required
def export_data():
    """数据导出"""
    # 这里可以实现数据导出功能
    flash('数据导出功能开发中', 'info')
    return redirect(url_for('admin.dashboard'))
