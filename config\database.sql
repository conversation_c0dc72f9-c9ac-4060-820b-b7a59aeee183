-- 黔南民族师范学院学生情绪分析系统数据库初始化脚本

-- 创建数据库
CREATE DATABASE IF NOT EXISTS emotion_analysis CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE emotion_analysis;

-- 用户表
CREATE TABLE users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL COMMENT '用户名',
    password_hash VARCHAR(255) NOT NULL COMMENT '密码哈希',
    email VARCHAR(100) UNIQUE COMMENT '邮箱',
    student_id VARCHAR(20) UNIQUE COMMENT '学号',
    real_name VARCHAR(50) COMMENT '真实姓名',
    department VARCHAR(100) COMMENT '院系',
    major VARCHAR(100) COMMENT '专业',
    grade VARCHAR(10) COMMENT '年级',
    role ENUM('student', 'teacher', 'admin') DEFAULT 'student' COMMENT '用户角色',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否激活',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) COMMENT '用户表';

-- 情绪分析记录表
CREATE TABLE emotion_records (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL COMMENT '用户ID',
    text_content TEXT NOT NULL COMMENT '分析文本内容',
    emotion_result JSON NOT NULL COMMENT '情绪分析结果(JSON格式)',
    dominant_emotion VARCHAR(20) NOT NULL COMMENT '主要情绪',
    confidence_score DECIMAL(5,4) NOT NULL COMMENT '置信度分数',
    analysis_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '分析时间',
    ip_address VARCHAR(45) COMMENT 'IP地址',
    user_agent TEXT COMMENT '用户代理',
    INDEX idx_user_id (user_id),
    INDEX idx_dominant_emotion (dominant_emotion),
    INDEX idx_analysis_time (analysis_time),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) COMMENT '情绪分析记录表';

-- 情绪统计表
CREATE TABLE emotion_statistics (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT COMMENT '用户ID(NULL表示全局统计)',
    date DATE NOT NULL COMMENT '统计日期',
    fear_count INT DEFAULT 0 COMMENT '恐惧情绪次数',
    anger_count INT DEFAULT 0 COMMENT '愤怒情绪次数',
    disgust_count INT DEFAULT 0 COMMENT '厌恶情绪次数',
    like_count INT DEFAULT 0 COMMENT '喜好情绪次数',
    sadness_count INT DEFAULT 0 COMMENT '悲伤情绪次数',
    joy_count INT DEFAULT 0 COMMENT '高兴情绪次数',
    surprise_count INT DEFAULT 0 COMMENT '惊讶情绪次数',
    total_count INT DEFAULT 0 COMMENT '总分析次数',
    avg_confidence DECIMAL(5,4) COMMENT '平均置信度',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY unique_user_date (user_id, date),
    INDEX idx_date (date),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) COMMENT '情绪统计表';

-- 系统配置表
CREATE TABLE system_config (
    id INT AUTO_INCREMENT PRIMARY KEY,
    config_key VARCHAR(100) UNIQUE NOT NULL COMMENT '配置键',
    config_value TEXT COMMENT '配置值',
    description TEXT COMMENT '配置描述',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) COMMENT '系统配置表';

-- 用户会话表
CREATE TABLE user_sessions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    session_token VARCHAR(255) UNIQUE NOT NULL COMMENT '会话令牌',
    expires_at TIMESTAMP NOT NULL COMMENT '过期时间',
    ip_address VARCHAR(45) COMMENT 'IP地址',
    user_agent TEXT COMMENT '用户代理',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_user_id (user_id),
    INDEX idx_session_token (session_token),
    INDEX idx_expires_at (expires_at),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) COMMENT '用户会话表';

-- 插入默认管理员用户
INSERT INTO users (username, password_hash, email, real_name, role) VALUES 
('admin', 'pbkdf2:sha256:260000$salt$hash', '<EMAIL>', '系统管理员', 'admin');

-- 插入系统配置
INSERT INTO system_config (config_key, config_value, description) VALUES 
('system_name', '黔南民族师范学院学生情绪分析系统', '系统名称'),
('max_text_length', '500', '最大文本长度'),
('analysis_rate_limit', '100', '每日分析次数限制'),
('model_version', 'v1.0.0', '模型版本');

-- 创建视图：用户情绪统计概览
CREATE VIEW user_emotion_overview AS
SELECT 
    u.id as user_id,
    u.username,
    u.real_name,
    u.department,
    u.major,
    COUNT(er.id) as total_analyses,
    AVG(er.confidence_score) as avg_confidence,
    SUM(CASE WHEN er.dominant_emotion = '高兴' THEN 1 ELSE 0 END) as joy_count,
    SUM(CASE WHEN er.dominant_emotion = '悲伤' THEN 1 ELSE 0 END) as sadness_count,
    SUM(CASE WHEN er.dominant_emotion = '愤怒' THEN 1 ELSE 0 END) as anger_count,
    SUM(CASE WHEN er.dominant_emotion = '恐惧' THEN 1 ELSE 0 END) as fear_count,
    SUM(CASE WHEN er.dominant_emotion = '厌恶' THEN 1 ELSE 0 END) as disgust_count,
    SUM(CASE WHEN er.dominant_emotion = '喜好' THEN 1 ELSE 0 END) as like_count,
    SUM(CASE WHEN er.dominant_emotion = '惊讶' THEN 1 ELSE 0 END) as surprise_count,
    MAX(er.analysis_time) as last_analysis_time
FROM users u
LEFT JOIN emotion_records er ON u.id = er.user_id
WHERE u.role = 'student'
GROUP BY u.id, u.username, u.real_name, u.department, u.major;
