"""
StructBERT情绪分析模型测试脚本
"""
import os
import sys
import torch
from transformers import AutoTokenizer, AutoModelForSequenceClassification

def test_model_loading():
    """测试模型加载"""
    print("=" * 60)
    print("StructBERT情绪分析模型测试")
    print("=" * 60)
    
    model_path = 'nlp_structbert_emotion-classification_chinese-base'
    
    # 检查模型文件
    print("1. 检查模型文件...")
    required_files = ['config.json', 'pytorch_model.bin', 'vocab.txt']
    
    if not os.path.exists(model_path):
        print(f"❌ 模型目录不存在: {model_path}")
        return False
    
    for file in required_files:
        file_path = os.path.join(model_path, file)
        if os.path.exists(file_path):
            print(f"✅ {file} - 存在")
        else:
            print(f"❌ {file} - 缺失")
            return False
    
    # 检查设备
    print("\n2. 检查计算设备...")
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")
    if torch.cuda.is_available():
        print(f"GPU: {torch.cuda.get_device_name(0)}")
        print(f"显存: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB")
    
    # 加载模型
    print("\n3. 加载模型...")
    try:
        tokenizer = AutoTokenizer.from_pretrained(model_path)
        model = AutoModelForSequenceClassification.from_pretrained(model_path)
        model.to(device)
        model.eval()
        print("✅ 模型加载成功")
    except Exception as e:
        print(f"❌ 模型加载失败: {e}")
        return False
    
    # 情绪标签
    emotion_labels = {
        0: "恐惧",
        1: "愤怒", 
        2: "厌恶",
        3: "喜好",
        4: "悲伤",
        5: "高兴",
        6: "惊讶"
    }
    
    print(f"✅ 支持的情绪类型: {list(emotion_labels.values())}")
    
    # 测试样本
    test_texts = [
        "今天考试考得很好，我很开心！",
        "最近学习压力很大，感觉很焦虑。",
        "室友总是很吵，让我很生气。",
        "明天要面试了，有点紧张害怕。",
        "食堂的饭菜质量越来越差了，真恶心。",
        "我很喜欢这门课程，老师讲得很好。",
        "刚才发生的事情让我很震惊！"
    ]
    
    print("\n4. 测试情绪分析...")
    print("-" * 60)
    
    for i, text in enumerate(test_texts, 1):
        try:
            # 编码文本
            inputs = tokenizer(
                text,
                return_tensors="pt",
                truncation=True,
                padding=True,
                max_length=512
            )
            
            # 移动到设备
            inputs = {k: v.to(device) for k, v in inputs.items()}
            
            # 模型推理
            with torch.no_grad():
                outputs = model(**inputs)
                logits = outputs.logits
                probabilities = torch.softmax(logits, dim=-1)
                scores = probabilities.cpu().numpy()[0]
            
            # 找出最高分的情绪
            max_idx = scores.argmax()
            max_emotion = emotion_labels[max_idx]
            max_score = scores[max_idx]
            
            print(f"测试 {i}: {text}")
            print(f"结果: {max_emotion} (置信度: {max_score:.4f})")
            
            # 显示所有情绪的分数
            print("详细分数:")
            for idx, score in enumerate(scores):
                emotion = emotion_labels[idx]
                print(f"  {emotion}: {score:.4f}")
            print("-" * 60)
            
        except Exception as e:
            print(f"❌ 测试 {i} 失败: {e}")
            return False
    
    print("✅ 所有测试通过！")
    return True

def test_batch_processing():
    """测试批量处理"""
    print("\n5. 测试批量处理...")
    
    model_path = 'nlp_structbert_emotion-classification_chinese-base'
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    try:
        tokenizer = AutoTokenizer.from_pretrained(model_path)
        model = AutoModelForSequenceClassification.from_pretrained(model_path)
        model.to(device)
        model.eval()
        
        # 批量文本
        batch_texts = [
            "今天天气很好，心情不错。",
            "作业太多了，压力山大。",
            "和朋友一起吃饭很开心。"
        ]
        
        # 批量编码
        inputs = tokenizer(
            batch_texts,
            return_tensors="pt",
            truncation=True,
            padding=True,
            max_length=512
        )
        
        inputs = {k: v.to(device) for k, v in inputs.items()}
        
        # 批量推理
        with torch.no_grad():
            outputs = model(**inputs)
            logits = outputs.logits
            probabilities = torch.softmax(logits, dim=-1)
            scores = probabilities.cpu().numpy()
        
        emotion_labels = {0: "恐惧", 1: "愤怒", 2: "厌恶", 3: "喜好", 4: "悲伤", 5: "高兴", 6: "惊讶"}
        
        print("批量处理结果:")
        for i, (text, score_array) in enumerate(zip(batch_texts, scores)):
            max_idx = score_array.argmax()
            max_emotion = emotion_labels[max_idx]
            max_score = score_array[max_idx]
            print(f"  文本 {i+1}: {text}")
            print(f"  情绪: {max_emotion} (置信度: {max_score:.4f})")
        
        print("✅ 批量处理测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 批量处理测试失败: {e}")
        return False

def performance_test():
    """性能测试"""
    print("\n6. 性能测试...")
    
    import time
    
    model_path = 'nlp_structbert_emotion-classification_chinese-base'
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    try:
        tokenizer = AutoTokenizer.from_pretrained(model_path)
        model = AutoModelForSequenceClassification.from_pretrained(model_path)
        model.to(device)
        model.eval()
        
        test_text = "这是一个用于性能测试的示例文本，包含了一些情绪表达。"
        
        # 预热
        for _ in range(3):
            inputs = tokenizer(test_text, return_tensors="pt", truncation=True, padding=True, max_length=512)
            inputs = {k: v.to(device) for k, v in inputs.items()}
            with torch.no_grad():
                outputs = model(**inputs)
        
        # 性能测试
        num_tests = 100
        start_time = time.time()
        
        for _ in range(num_tests):
            inputs = tokenizer(test_text, return_tensors="pt", truncation=True, padding=True, max_length=512)
            inputs = {k: v.to(device) for k, v in inputs.items()}
            with torch.no_grad():
                outputs = model(**inputs)
        
        end_time = time.time()
        total_time = end_time - start_time
        avg_time = total_time / num_tests
        
        print(f"性能测试结果:")
        print(f"  总测试次数: {num_tests}")
        print(f"  总耗时: {total_time:.2f} 秒")
        print(f"  平均耗时: {avg_time*1000:.2f} 毫秒/次")
        print(f"  处理速度: {1/avg_time:.1f} 次/秒")
        
        print("✅ 性能测试完成！")
        return True
        
    except Exception as e:
        print(f"❌ 性能测试失败: {e}")
        return False

def main():
    """主测试函数"""
    success = True
    
    # 基础功能测试
    if not test_model_loading():
        success = False
    
    # 批量处理测试
    if success and not test_batch_processing():
        success = False
    
    # 性能测试
    if success and not performance_test():
        success = False
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 所有测试通过！模型集成成功！")
        print("\n下一步:")
        print("1. 运行 python run.py 启动Web应用")
        print("2. 访问 http://localhost:5000")
        print("3. 注册用户并开始使用情绪分析功能")
    else:
        print("❌ 测试失败！请检查模型文件和环境配置。")
    print("=" * 60)

if __name__ == "__main__":
    main()
