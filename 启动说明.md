# 黔南民族师范学院学生情绪分析系统 - 启动说明

## 🚀 快速启动步骤

### 1. 环境准备
确保你的系统已安装：
- Python 3.8+
- MySQL 8.0+
- Git

### 2. 数据库配置
```sql
-- 登录MySQL
mysql -u root -p

-- 创建数据库
CREATE DATABASE emotion_analysis CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 使用数据库
USE emotion_analysis;

-- 导入数据库结构
SOURCE config/database.sql;

-- 导入测试数据
SOURCE config/test_data.sql;

-- 退出MySQL
EXIT;
```

### 3. 安装Python依赖
```bash
# 方法1：使用pip
pip install flask flask-sqlalchemy flask-login flask-cors transformers torch pymysql

# 方法2：使用conda
conda install flask flask-sqlalchemy flask-login flask-cors -c conda-forge
pip install transformers torch pymysql
```

### 4. 配置文件修改
编辑 `config/config.py` 文件，修改数据库密码：
```python
MYSQL_PASSWORD = os.environ.get('MYSQL_PASSWORD') or '你的MySQL密码'
```

### 5. 启动应用
```bash
# 设置环境变量（Windows）
set FLASK_ENV=development

# 设置环境变量（Linux/Mac）
export FLASK_ENV=development

# 启动应用
python run.py
```

### 6. 访问系统
打开浏览器访问：http://localhost:5000

## 📋 测试账户

### 管理员账户
- 用户名：`admin`
- 密码：`admin123`
- 权限：系统管理员，可查看所有数据

### 教师账户
- 用户名：`teacher1`, `teacher2`, `teacher3`
- 密码：`teacher123`
- 权限：教师工作台，可查看本专业学生数据
- 专业：teacher1(计算机)，teacher2(数学)，teacher3(文学)

### 学生账户
- 用户名：`student1` - `student8`
- 密码：`student123`
- 权限：情绪分析功能，个人数据查看
- 分布：不同专业和年级的学生

## 🔧 问题排查

### 1. 模块导入错误
如果遇到 `ModuleNotFoundError`，请确保已安装所有依赖：
```bash
pip install -r requirements.txt
```

### 2. 数据库连接错误
- 检查MySQL服务是否启动
- 确认数据库名称、用户名、密码是否正确
- 检查 `config/config.py` 中的数据库配置

### 3. 模型加载错误
如果AI模型加载失败：
- 确保 `nlp_structbert_emotion-classification_chinese-base` 文件夹存在
- 检查模型文件是否完整
- 可以先运行 `python test_model.py` 测试模型

### 4. 端口占用
如果5000端口被占用，可以修改 `run.py` 中的端口号：
```python
app.run(host='0.0.0.0', port=8080, debug=True)
```

## 📁 项目结构说明

```
emo2/
├── app/                    # Flask应用主目录
│   ├── __init__.py        # 应用工厂
│   ├── models/            # 数据模型
│   ├── routes/            # 路由控制器
│   ├── services/          # 业务逻辑服务
│   └── templates/         # HTML模板
├── config/                # 配置文件
│   ├── config.py         # 应用配置
│   ├── database.sql      # 数据库结构
│   └── test_data.sql     # 测试数据
├── static/               # 静态文件
├── nlp_structbert_emotion-classification_chinese-base/  # AI模型
├── requirements.txt      # Python依赖
├── run.py               # 应用启动文件
└── test_model.py        # 模型测试脚本
```

## 🎯 功能测试

### 1. 用户注册登录
- 访问 `/register` 注册新用户
- 访问 `/login` 登录系统
- 测试不同角色的权限

### 2. 情绪分析
- 登录后访问 `/emotion/analyze`
- 输入中文文本进行情绪分析
- 查看分析结果和历史记录

### 3. 数据统计
- 访问 `/emotion/statistics` 查看个人统计
- 管理员可访问 `/admin` 查看全局统计

### 4. 教师功能
- 使用教师账户登录 (如teacher1)
- 访问教师工作台查看本专业学生数据
- 学生管理、情绪分析报告、心理健康评估

### 5. 管理功能
- 使用管理员账户登录
- 访问管理后台查看用户和数据管理

## 📞 技术支持

如果遇到问题，请检查：
1. Python版本是否为3.8+
2. MySQL服务是否正常运行
3. 所有依赖是否正确安装
4. 数据库配置是否正确
5. 模型文件是否存在

## 🎉 开发完成

系统已完成以下功能：
- ✅ 用户认证和权限管理
- ✅ StructBERT情绪分析
- ✅ 数据可视化统计
- ✅ 教师工作台和学生管理
- ✅ 管理员后台
- ✅ 响应式Web界面
- ✅ 完整的测试数据

祝你的毕业设计顺利完成！🎓
