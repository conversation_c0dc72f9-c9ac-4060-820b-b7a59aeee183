"""
主要路由
"""
from flask import Blueprint, render_template, redirect, url_for
from flask_login import login_required, current_user

main_bp = Blueprint('main', __name__)

@main_bp.route('/')
def index():
    """首页"""
    if current_user.is_authenticated:
        return redirect(url_for('main.dashboard'))
    return render_template('index.html')

@main_bp.route('/dashboard')
@login_required
def dashboard():
    """用户仪表板"""
    from app.models.emotion_record import EmotionRecord
    
    # 获取用户最近的分析记录
    recent_records = EmotionRecord.get_user_recent_records(current_user.id, limit=5)
    
    # 获取用户情绪统计
    emotion_stats = current_user.get_dominant_emotions(days=30)
    
    # 获取分析次数
    analysis_count = current_user.get_emotion_count(days=30)
    
    return render_template('dashboard.html', 
                         recent_records=recent_records,
                         emotion_stats=emotion_stats,
                         analysis_count=analysis_count)

@main_bp.route('/about')
def about():
    """关于页面"""
    return render_template('about.html')

@main_bp.route('/help')
def help():
    """帮助页面"""
    return render_template('help.html')
