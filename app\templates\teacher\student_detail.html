{% extends "base.html" %}

{% block title %}{{ student.real_name or student.username }} - 学生详情{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2>
                <i class="fas fa-user me-2"></i>学生详情
            </h2>
            <div>
                <div class="btn-group" role="group">
                    <a href="?days=7" class="btn btn-outline-primary {{ 'active' if days == 7 else '' }}">7天</a>
                    <a href="?days=30" class="btn btn-outline-primary {{ 'active' if days == 30 else '' }}">30天</a>
                    <a href="?days=90" class="btn btn-outline-primary {{ 'active' if days == 90 else '' }}">90天</a>
                </div>
                <a href="{{ url_for('teacher.students') }}" class="btn btn-outline-secondary ms-2">
                    <i class="fas fa-arrow-left me-2"></i>返回列表
                </a>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- 学生基本信息 -->
    <div class="col-md-4 mb-4">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">
                    <i class="fas fa-id-card me-2"></i>基本信息
                </h5>
            </div>
            <div class="card-body">
                <div class="text-center mb-3">
                    <i class="fas fa-user-circle fa-4x text-muted mb-2"></i>
                    <h5>{{ student.real_name or student.username }}</h5>
                    <span class="badge bg-success">学生</span>
                </div>
                
                <table class="table table-borderless table-sm">
                    <tr>
                        <th width="35%">学号:</th>
                        <td>{{ student.student_id or '-' }}</td>
                    </tr>
                    <tr>
                        <th>用户名:</th>
                        <td>{{ student.username }}</td>
                    </tr>
                    <tr>
                        <th>邮箱:</th>
                        <td>{{ student.email or '-' }}</td>
                    </tr>
                    <tr>
                        <th>院系:</th>
                        <td>{{ student.department or '-' }}</td>
                    </tr>
                    <tr>
                        <th>专业:</th>
                        <td>{{ student.major or '-' }}</td>
                    </tr>
                    <tr>
                        <th>年级:</th>
                        <td>{{ student.grade or '-' }}</td>
                    </tr>
                    <tr>
                        <th>注册时间:</th>
                        <td>
                            {% if student.created_at %}
                            {{ student.created_at.strftime('%Y-%m-%d') }}
                            {% else %}
                            -
                            {% endif %}
                        </td>
                    </tr>
                </table>
            </div>
        </div>
        
        <!-- 心理健康评估 -->
        <div class="card mt-3">
            <div class="card-header bg-info text-white">
                <h6 class="mb-0">
                    <i class="fas fa-heart me-2"></i>心理健康评估
                </h6>
            </div>
            <div class="card-body">
                {% if total_count > 0 %}
                {% set positive_emotions = emotion_stats.get('高兴', 0) + emotion_stats.get('喜好', 0) %}
                {% set negative_emotions = emotion_stats.get('悲伤', 0) + emotion_stats.get('愤怒', 0) + emotion_stats.get('恐惧', 0) + emotion_stats.get('厌恶', 0) %}
                {% set positive_ratio = positive_emotions / total_count %}
                {% set negative_ratio = negative_emotions / total_count %}
                
                <div class="text-center mb-3">
                    <div class="h4 mb-1">
                        {% if health_status == 'good' %}
                        <span class="text-success">良好</span>
                        {% elif health_status == 'normal' %}
                        <span class="text-warning">一般</span>
                        {% elif health_status == 'concern' %}
                        <span class="text-danger">需要关注</span>
                        {% else %}
                        <span class="text-muted">数据不足</span>
                        {% endif %}
                    </div>
                    <small class="text-muted">基于最近{{ days }}天的分析</small>
                </div>
                
                <div class="mb-2">
                    <small class="text-muted">积极情绪比例</small>
                    <div class="progress" style="height: 20px;">
                        <div class="progress-bar bg-success" style="width: {{ (positive_ratio * 100) | round(1) }}%">
                            {{ (positive_ratio * 100) | round(1) }}%
                        </div>
                    </div>
                </div>
                
                <div class="mb-2">
                    <small class="text-muted">消极情绪比例</small>
                    <div class="progress" style="height: 20px;">
                        <div class="progress-bar bg-danger" style="width: {{ (negative_ratio * 100) | round(1) }}%">
                            {{ (negative_ratio * 100) | round(1) }}%
                        </div>
                    </div>
                </div>
                
                <div class="mt-3">
                    <h6 class="small">建议:</h6>
                    <ul class="small mb-0">
                        {% if health_status == 'good' %}
                        <li class="text-success">情绪状态良好，继续保持</li>
                        {% elif health_status == 'concern' %}
                        <li class="text-danger">建议关注学生心理状态</li>
                        <li class="text-danger">可考虑进行心理疏导</li>
                        {% else %}
                        <li class="text-info">持续关注情绪变化</li>
                        {% endif %}
                        <li>鼓励学生多参与积极活动</li>
                    </ul>
                </div>
                {% else %}
                <div class="text-center text-muted">
                    <i class="fas fa-chart-bar fa-2x mb-2"></i>
                    <p class="mb-0">暂无分析数据</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
    
    <!-- 统计图表 -->
    <div class="col-md-8">
        <!-- 统计概览 -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card bg-primary text-white">
                    <div class="card-body text-center">
                        <h4 class="mb-0">{{ total_count }}</h4>
                        <p class="mb-0 small">总分析次数</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-success text-white">
                    <div class="card-body text-center">
                        <h4 class="mb-0">{{ emotion_stats.get('高兴', 0) + emotion_stats.get('喜好', 0) }}</h4>
                        <p class="mb-0 small">积极情绪</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-warning text-white">
                    <div class="card-body text-center">
                        <h4 class="mb-0">{{ emotion_stats.get('悲伤', 0) + emotion_stats.get('愤怒', 0) + emotion_stats.get('恐惧', 0) + emotion_stats.get('厌恶', 0) }}</h4>
                        <p class="mb-0 small">消极情绪</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-info text-white">
                    <div class="card-body text-center">
                        <h4 class="mb-0">{{ days }}</h4>
                        <p class="mb-0 small">统计天数</p>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 图表区域 -->
        <div class="row">
            <!-- 情绪分布 -->
            <div class="col-md-6 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-chart-pie me-2"></i>情绪分布
                        </h6>
                    </div>
                    <div class="card-body">
                        {% if emotion_stats %}
                        <canvas id="emotionPieChart" width="300" height="300"></canvas>
                        {% else %}
                        <div class="text-center text-muted py-4">
                            <i class="fas fa-chart-pie fa-3x mb-3"></i>
                            <p>暂无数据</p>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
            
            <!-- 情绪趋势 -->
            <div class="col-md-6 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-chart-line me-2"></i>情绪趋势
                        </h6>
                    </div>
                    <div class="card-body">
                        {% if trend_data %}
                        <canvas id="emotionTrendChart" width="300" height="300"></canvas>
                        {% else %}
                        <div class="text-center text-muted py-4">
                            <i class="fas fa-chart-line fa-3x mb-3"></i>
                            <p>暂无趋势数据</p>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 最近分析记录 -->
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-history me-2"></i>最近分析记录 (最多显示50条)
                </h6>
            </div>
            <div class="card-body">
                {% if records %}
                <div class="table-responsive">
                    <table class="table table-sm table-hover">
                        <thead>
                            <tr>
                                <th width="40%">文本内容</th>
                                <th width="15%">主要情绪</th>
                                <th width="15%">置信度</th>
                                <th width="20%">分析时间</th>
                                <th width="10%">操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for record in records %}
                            <tr>
                                <td>
                                    <div style="max-width: 200px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;" 
                                         title="{{ record.text_content }}">
                                        {{ record.text_content }}
                                    </div>
                                </td>
                                <td>
                                    {% set emotion_colors = {
                                        '高兴': 'warning',
                                        '悲伤': 'primary', 
                                        '愤怒': 'danger',
                                        '恐惧': 'dark',
                                        '厌恶': 'success',
                                        '喜好': 'info',
                                        '惊讶': 'secondary'
                                    } %}
                                    <span class="badge bg-{{ emotion_colors.get(record.dominant_emotion, 'secondary') }}">
                                        {{ record.dominant_emotion }}
                                    </span>
                                </td>
                                <td>
                                    <div class="progress" style="height: 15px;">
                                        <div class="progress-bar bg-{{ emotion_colors.get(record.dominant_emotion, 'secondary') }}" 
                                             style="width: {{ (record.confidence_score * 100) | round(1) }}%">
                                        </div>
                                    </div>
                                    <small>{{ (record.confidence_score * 100) | round(1) }}%</small>
                                </td>
                                <td>
                                    <small>
                                        {{ record.analysis_time.strftime('%m-%d %H:%M') }}
                                    </small>
                                </td>
                                <td>
                                    <a href="{{ url_for('emotion.result', record_id=record.id) }}" 
                                       class="btn btn-sm btn-outline-info" title="查看详情">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center text-muted py-4">
                    <i class="fas fa-inbox fa-3x mb-3"></i>
                    <h6>暂无分析记录</h6>
                    <p class="mb-0">该学生在最近{{ days }}天内没有进行情绪分析</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
{% if emotion_stats %}
<script>
// 情绪分布饼图
const pieCtx = document.getElementById('emotionPieChart').getContext('2d');
const emotionPieChart = new Chart(pieCtx, {
    type: 'doughnut',
    data: {
        labels: {{ emotion_stats.keys() | list | tojson }},
        datasets: [{
            data: {{ emotion_stats.values() | list | tojson }},
            backgroundColor: [
                '#FFD700', // 高兴
                '#4169E1', // 悲伤
                '#FF4500', // 愤怒
                '#8B4513', // 恐惧
                '#9ACD32', // 厌恶
                '#FF69B4', // 喜好
                '#9370DB'  // 惊讶
            ],
            borderWidth: 2,
            borderColor: '#fff'
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'bottom',
                labels: {
                    padding: 10,
                    usePointStyle: true,
                    font: {
                        size: 11
                    }
                }
            }
        }
    }
});
</script>
{% endif %}

{% if trend_data %}
<script>
// 情绪趋势图
const trendCtx = document.getElementById('emotionTrendChart').getContext('2d');
const trendData = {{ trend_data | tojson }};
const dates = Object.keys(trendData).sort();
const emotions = ['高兴', '悲伤', '愤怒', '恐惧', '厌恶', '喜好', '惊讶'];
const colors = ['#FFD700', '#4169E1', '#FF4500', '#8B4513', '#9ACD32', '#FF69B4', '#9370DB'];

const datasets = emotions.map((emotion, index) => ({
    label: emotion,
    data: dates.map(date => trendData[date][emotion] || 0),
    borderColor: colors[index],
    backgroundColor: colors[index] + '20',
    tension: 0.4,
    fill: false,
    pointRadius: 3
}));

const emotionTrendChart = new Chart(trendCtx, {
    type: 'line',
    data: {
        labels: dates.map(date => date.substring(5)), // 只显示月-日
        datasets: datasets
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'bottom',
                labels: {
                    font: {
                        size: 10
                    },
                    usePointStyle: true
                }
            }
        },
        scales: {
            y: {
                beginAtZero: true,
                ticks: {
                    stepSize: 1,
                    font: {
                        size: 10
                    }
                }
            },
            x: {
                ticks: {
                    font: {
                        size: 10
                    }
                }
            }
        }
    }
});
</script>
{% endif %}
{% endblock %}
