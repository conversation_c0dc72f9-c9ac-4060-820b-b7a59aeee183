# 教师端功能测试指南

## 🎯 教师端功能概述

教师端专门为教师用户设计，提供以下核心功能：

### 1. 教师工作台 (`/teacher/dashboard`)
- **专业学生统计**: 显示本专业学生总数、活跃学生数等
- **分析趋势图**: 最近7天的情绪分析趋势
- **情绪分布图**: 专业整体情绪分布饼图
- **需要关注的学生**: 自动识别消极情绪较多的学生
- **活跃学生排行**: 本周最活跃的学生TOP 10
- **快捷操作**: 学生管理、分析报告、数据导出等

### 2. 学生管理 (`/teacher/students`)
- **学生列表**: 显示本专业所有学生
- **活跃度统计**: 总分析次数、本周分析次数
- **状态标识**: 未激活、不活跃、正常、很活跃
- **最近情绪**: 显示学生最近的情绪状态
- **分页浏览**: 支持大量学生数据的分页显示

### 3. 学生详情 (`/teacher/student/<id>`)
- **基本信息**: 学生的详细资料
- **心理健康评估**: 基于情绪数据的自动评估
- **情绪统计图表**: 饼图和趋势图
- **分析记录**: 最近50条分析记录
- **时间筛选**: 支持7天、30天、90天数据查看

### 4. 专业分析报告 (`/teacher/analytics`)
- **整体概览**: 专业学生总数、活跃度、使用率
- **情绪分布**: 专业整体情绪分布分析
- **年级对比**: 不同年级的情绪状态对比
- **活跃度排行**: 学生使用系统的活跃度排名
- **分析建议**: 基于数据的专业建议

## 🔐 权限控制

### 教师权限范围
- **只能查看本专业学生**: 教师只能访问与自己相同院系和专业的学生数据
- **无法修改学生数据**: 教师只有查看权限，无法修改学生信息
- **无法删除分析记录**: 保护学生隐私，教师无法删除学生的分析记录
- **数据统计权限**: 可以查看汇总统计数据，但不能查看具体文本内容

### 安全机制
- **身份验证**: 必须是教师或管理员角色才能访问
- **数据隔离**: 自动过滤，只显示本专业学生数据
- **权限检查**: 每个页面都有权限验证装饰器

## 📊 数据分析功能

### 心理健康评估算法
```python
# 评估标准
if positive_ratio > 0.4 and negative_ratio < 0.3:
    health_status = 'good'      # 良好
elif positive_ratio > 0.2 and negative_ratio < 0.5:
    health_status = 'normal'    # 一般
else:
    health_status = 'concern'   # 需要关注
```

### 需要关注学生识别
- **消极情绪比例 > 60%**: 自动标记为需要关注
- **本周分析次数 > 0**: 确保有足够数据支撑
- **按消极情绪比例排序**: 优先显示最需要关注的学生

### 活跃度分级
- **很活跃**: 本周分析 > 5次
- **正常**: 本周分析 1-5次
- **不活跃**: 本周分析 = 0次，但历史有记录
- **未激活**: 从未使用过系统

## 🧪 测试步骤

### 1. 登录教师账户
```
用户名: teacher1
密码: teacher123
专业: 计算机科学与技术
```

### 2. 访问教师工作台
- 点击右上角用户菜单中的"教师工作台"
- 或直接访问: `http://localhost:5000/teacher/dashboard`

### 3. 查看学生管理
- 在工作台点击"学生管理"按钮
- 或访问: `http://localhost:5000/teacher/students`

### 4. 查看学生详情
- 在学生列表中点击"查看"按钮
- 或访问: `http://localhost:5000/teacher/student/5` (student1的ID)

### 5. 查看分析报告
- 在工作台点击"分析报告"按钮
- 或访问: `http://localhost:5000/teacher/analytics`

## 📋 测试用例

### 测试用例1: 权限验证
1. 使用student1账户登录
2. 尝试访问 `/teacher/dashboard`
3. **期望结果**: 被重定向到首页，显示权限错误

### 测试用例2: 数据隔离
1. 使用teacher1账户登录 (计算机专业)
2. 查看学生列表
3. **期望结果**: 只显示计算机专业的学生(student1)

### 测试用例3: 学生详情查看
1. 使用teacher1账户登录
2. 查看student1的详情页面
3. **期望结果**: 显示完整的学生信息和分析数据

### 测试用例4: 跨专业访问限制
1. 使用teacher1账户登录
2. 尝试访问其他专业学生详情 (如student2)
3. **期望结果**: 显示权限错误，无法访问

## 🎨 界面特色

### 响应式设计
- **桌面端**: 完整的图表和表格显示
- **移动端**: 自适应布局，保持功能完整性

### 数据可视化
- **Chart.js图表**: 饼图、折线图、柱状图
- **Bootstrap进度条**: 直观显示比例数据
- **颜色编码**: 不同情绪使用不同颜色标识

### 用户体验
- **面包屑导航**: 清晰的页面层级关系
- **快捷操作**: 一键跳转到相关功能
- **状态标识**: 直观的徽章和图标系统

## 🔧 技术实现

### 后端架构
- **Flask Blueprint**: 模块化路由管理
- **权限装饰器**: `@teacher_required` 统一权限控制
- **SQLAlchemy查询**: 高效的数据库查询和统计
- **数据聚合**: 复杂的统计分析查询

### 前端技术
- **Bootstrap 5**: 现代化UI框架
- **Chart.js**: 专业的图表库
- **Jinja2模板**: 服务端渲染
- **响应式设计**: 移动端适配

## 📈 数据统计说明

### 统计维度
- **时间维度**: 支持7天、30天、90天统计
- **情绪维度**: 7种情绪类型的分布统计
- **用户维度**: 个人和群体统计对比
- **年级维度**: 不同年级的对比分析

### 统计指标
- **使用率**: 活跃学生数 / 总学生数
- **活跃度**: 平均分析次数、频率分布
- **情绪健康**: 积极/消极情绪比例
- **趋势分析**: 时间序列变化趋势

这套教师端功能为教师提供了全面的学生情绪监控和分析工具，有助于及时发现和关注学生的心理健康状况。
