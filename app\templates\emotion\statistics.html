{% extends "base.html" %}

{% block title %}统计分析 - {{ SYSTEM_NAME }}{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2>
                <i class="fas fa-chart-bar me-2"></i>个人情绪统计分析
            </h2>
            <div class="btn-group" role="group">
                <a href="?days=7" class="btn btn-outline-primary {{ 'active' if days == 7 else '' }}">7天</a>
                <a href="?days=30" class="btn btn-outline-primary {{ 'active' if days == 30 else '' }}">30天</a>
                <a href="?days=90" class="btn btn-outline-primary {{ 'active' if days == 90 else '' }}">90天</a>
            </div>
        </div>
    </div>
</div>

<!-- 统计概览 -->
<div class="row mb-4">
    <div class="col-md-3 mb-3">
        <div class="card bg-primary text-white">
            <div class="card-body text-center">
                <i class="fas fa-search fa-2x mb-2"></i>
                <h4 class="mb-0">{{ total_count }}</h4>
                <p class="mb-0">总分析次数</p>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card bg-success text-white">
            <div class="card-body text-center">
                <i class="fas fa-smile fa-2x mb-2"></i>
                <h4 class="mb-0">{{ emotion_stats.get('高兴', 0) }}</h4>
                <p class="mb-0">积极情绪</p>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card bg-warning text-white">
            <div class="card-body text-center">
                <i class="fas fa-frown fa-2x mb-2"></i>
                <h4 class="mb-0">{{ emotion_stats.get('悲伤', 0) + emotion_stats.get('愤怒', 0) + emotion_stats.get('恐惧', 0) }}</h4>
                <p class="mb-0">消极情绪</p>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card bg-info text-white">
            <div class="card-body text-center">
                <i class="fas fa-calendar fa-2x mb-2"></i>
                <h4 class="mb-0">{{ days }}</h4>
                <p class="mb-0">统计天数</p>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- 情绪分布饼图 -->
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-pie me-2"></i>情绪分布
                </h5>
            </div>
            <div class="card-body">
                {% if emotion_stats %}
                <canvas id="emotionPieChart" width="400" height="400"></canvas>
                {% else %}
                <div class="text-center text-muted py-5">
                    <i class="fas fa-chart-pie fa-3x mb-3"></i>
                    <p>暂无数据</p>
                    <a href="{{ url_for('emotion.analyze') }}" class="btn btn-primary">
                        开始分析
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
    
    <!-- 情绪趋势图 -->
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-line me-2"></i>情绪趋势
                </h5>
            </div>
            <div class="card-body">
                {% if trend_data %}
                <canvas id="emotionTrendChart" width="400" height="400"></canvas>
                {% else %}
                <div class="text-center text-muted py-5">
                    <i class="fas fa-chart-line fa-3x mb-3"></i>
                    <p>暂无趋势数据</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- 详细统计表格 -->
{% if emotion_stats %}
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-table me-2"></i>详细统计数据
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>情绪类型</th>
                                <th>出现次数</th>
                                <th>占比</th>
                                <th>趋势</th>
                                <th>建议</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% set emotion_colors = {
                                '高兴': 'warning',
                                '悲伤': 'primary', 
                                '愤怒': 'danger',
                                '恐惧': 'dark',
                                '厌恶': 'success',
                                '喜好': 'info',
                                '惊讶': 'secondary'
                            } %}
                            
                            {% for emotion, count in emotion_stats.items() %}
                            {% set percentage = (count / total_count * 100) if total_count > 0 else 0 %}
                            <tr>
                                <td>
                                    <span class="badge bg-{{ emotion_colors.get(emotion, 'secondary') }} me-2">
                                        {{ emotion }}
                                    </span>
                                </td>
                                <td>{{ count }}</td>
                                <td>
                                    <div class="progress" style="height: 20px; width: 100px;">
                                        <div class="progress-bar bg-{{ emotion_colors.get(emotion, 'secondary') }}" 
                                             role="progressbar" 
                                             style="width: {{ percentage }}%">
                                            {{ percentage | round(1) }}%
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    {% if percentage > 30 %}
                                    <i class="fas fa-arrow-up text-danger"></i> 偏高
                                    {% elif percentage > 15 %}
                                    <i class="fas fa-minus text-warning"></i> 正常
                                    {% else %}
                                    <i class="fas fa-arrow-down text-success"></i> 偏低
                                    {% endif %}
                                </td>
                                <td class="small">
                                    {% if emotion == '高兴' %}
                                    保持积极心态
                                    {% elif emotion == '悲伤' %}
                                    {% if percentage > 25 %}需要关注心理健康{% else %}正常情绪波动{% endif %}
                                    {% elif emotion == '愤怒' %}
                                    {% if percentage > 20 %}建议学习情绪管理{% else %}适度表达情绪{% endif %}
                                    {% elif emotion == '恐惧' %}
                                    {% if percentage > 15 %}寻求支持和帮助{% else %}正常的谨慎态度{% endif %}
                                    {% elif emotion == '厌恶' %}
                                    识别并远离负面因素
                                    {% elif emotion == '喜好' %}
                                    培养更多兴趣爱好
                                    {% elif emotion == '惊讶' %}
                                    保持好奇心和开放心态
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 情绪健康评估 -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-heart me-2"></i>情绪健康评估
                </h5>
            </div>
            <div class="card-body">
                {% set positive_emotions = emotion_stats.get('高兴', 0) + emotion_stats.get('喜好', 0) %}
                {% set negative_emotions = emotion_stats.get('悲伤', 0) + emotion_stats.get('愤怒', 0) + emotion_stats.get('恐惧', 0) + emotion_stats.get('厌恶', 0) %}
                {% set neutral_emotions = emotion_stats.get('惊讶', 0) %}
                
                {% if total_count > 0 %}
                {% set positive_ratio = positive_emotions / total_count %}
                {% set negative_ratio = negative_emotions / total_count %}
                
                <div class="row">
                    <div class="col-md-4">
                        <div class="text-center p-3 {{ 'bg-success' if positive_ratio > 0.4 else 'bg-warning' if positive_ratio > 0.2 else 'bg-danger' }} text-white rounded">
                            <h4>{{ (positive_ratio * 100) | round(1) }}%</h4>
                            <p class="mb-0">积极情绪比例</p>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="text-center p-3 {{ 'bg-success' if negative_ratio < 0.3 else 'bg-warning' if negative_ratio < 0.5 else 'bg-danger' }} text-white rounded">
                            <h4>{{ (negative_ratio * 100) | round(1) }}%</h4>
                            <p class="mb-0">消极情绪比例</p>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="text-center p-3 bg-info text-white rounded">
                            <h4>
                                {% if positive_ratio > 0.4 and negative_ratio < 0.3 %}
                                优秀
                                {% elif positive_ratio > 0.2 and negative_ratio < 0.5 %}
                                良好
                                {% else %}
                                需要关注
                                {% endif %}
                            </h4>
                            <p class="mb-0">整体评估</p>
                        </div>
                    </div>
                </div>
                
                <div class="mt-4">
                    <h6>个性化建议：</h6>
                    <ul>
                        {% if positive_ratio > 0.4 %}
                        <li class="text-success">您的情绪状态很好，继续保持积极的心态！</li>
                        {% endif %}
                        
                        {% if negative_ratio > 0.5 %}
                        <li class="text-danger">消极情绪比例较高，建议寻求心理咨询师的帮助。</li>
                        {% elif negative_ratio > 0.3 %}
                        <li class="text-warning">注意调节情绪，可以尝试运动、音乐等放松方式。</li>
                        {% endif %}
                        
                        {% if positive_ratio < 0.2 %}
                        <li class="text-info">建议培养更多兴趣爱好，增加生活乐趣。</li>
                        {% endif %}
                        
                        <li>定期进行情绪记录和分析，有助于了解自己的情绪模式。</li>
                    </ul>
                </div>
                {% else %}
                <p class="text-muted">暂无足够数据进行评估，请继续使用系统进行情绪分析。</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

{% block extra_js %}
{% if emotion_stats %}
<script>
// 情绪分布饼图
const pieCtx = document.getElementById('emotionPieChart').getContext('2d');
const emotionPieChart = new Chart(pieCtx, {
    type: 'doughnut',
    data: {
        labels: {{ emotion_stats.keys() | list | tojson }},
        datasets: [{
            data: {{ emotion_stats.values() | list | tojson }},
            backgroundColor: [
                '#FFD700', // 高兴
                '#4169E1', // 悲伤
                '#FF4500', // 愤怒
                '#8B4513', // 恐惧
                '#9ACD32', // 厌恶
                '#FF69B4', // 喜好
                '#9370DB'  // 惊讶
            ],
            borderWidth: 2,
            borderColor: '#fff'
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'bottom',
                labels: {
                    padding: 15,
                    usePointStyle: true
                }
            },
            tooltip: {
                callbacks: {
                    label: function(context) {
                        const total = context.dataset.data.reduce((a, b) => a + b, 0);
                        const percentage = ((context.parsed / total) * 100).toFixed(1);
                        return context.label + ': ' + context.parsed + ' (' + percentage + '%)';
                    }
                }
            }
        }
    }
});

// 情绪趋势图
{% if trend_data %}
const trendCtx = document.getElementById('emotionTrendChart').getContext('2d');

// 处理趋势数据
const trendData = {{ trend_data | tojson }};
const dates = Object.keys(trendData).sort();
const emotions = ['高兴', '悲伤', '愤怒', '恐惧', '厌恶', '喜好', '惊讶'];
const colors = ['#FFD700', '#4169E1', '#FF4500', '#8B4513', '#9ACD32', '#FF69B4', '#9370DB'];

const datasets = emotions.map((emotion, index) => ({
    label: emotion,
    data: dates.map(date => trendData[date][emotion] || 0),
    borderColor: colors[index],
    backgroundColor: colors[index] + '20',
    tension: 0.4,
    fill: false
}));

const emotionTrendChart = new Chart(trendCtx, {
    type: 'line',
    data: {
        labels: dates,
        datasets: datasets
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'bottom'
            }
        },
        scales: {
            y: {
                beginAtZero: true,
                ticks: {
                    stepSize: 1
                }
            }
        }
    }
});
{% endif %}
</script>
{% endif %}
{% endblock %}
