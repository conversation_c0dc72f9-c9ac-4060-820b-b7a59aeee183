"""
黔南民族师范学院学生情绪分析系统配置文件
"""
import os
from datetime import timedelta

class Config:
    """基础配置类"""
    
    # 基本配置
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'qnnu-emotion-analysis-secret-key-2024'
    
    # 数据库配置
    MYSQL_HOST = os.environ.get('MYSQL_HOST') or 'localhost'
    MYSQL_PORT = int(os.environ.get('MYSQL_PORT') or 3306)
    MYSQL_USER = os.environ.get('MYSQL_USER') or 'root'
    MYSQL_PASSWORD = os.environ.get('MYSQL_PASSWORD') or 'Sshen2003821'
    MYSQL_DATABASE = os.environ.get('MYSQL_DATABASE') or 'emotion_analysis'
    
    SQLALCHEMY_DATABASE_URI = (
        f"mysql+pymysql://{MYSQL_USER}:{MYSQL_PASSWORD}@"
        f"{MYSQL_HOST}:{MYSQL_PORT}/{MYSQL_DATABASE}?charset=utf8mb4"
    )
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    SQLALCHEMY_ENGINE_OPTIONS = {
        'pool_size': 10,
        'pool_timeout': 20,
        'pool_recycle': -1,
        'max_overflow': 0
    }
    
    # Flask-Login配置
    REMEMBER_COOKIE_DURATION = timedelta(days=7)
    SESSION_PROTECTION = 'strong'
    
    # 文件上传配置
    MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16MB
    UPLOAD_FOLDER = 'uploads'
    
    # 情绪分析配置
    MODEL_PATH = 'nlp_structbert_emotion-classification_chinese-base'
    MAX_TEXT_LENGTH = 500
    BATCH_SIZE = 32
    
    # 速率限制配置
    RATELIMIT_STORAGE_URL = 'memory://'
    DAILY_ANALYSIS_LIMIT = 100
    
    # 日志配置
    LOG_LEVEL = 'INFO'
    LOG_FILE = 'logs/app.log'
    
    # 缓存配置
    CACHE_TYPE = 'simple'
    CACHE_DEFAULT_TIMEOUT = 300
    
    # 邮件配置（可选）
    MAIL_SERVER = os.environ.get('MAIL_SERVER')
    MAIL_PORT = int(os.environ.get('MAIL_PORT') or 587)
    MAIL_USE_TLS = os.environ.get('MAIL_USE_TLS', 'true').lower() in ['true', 'on', '1']
    MAIL_USERNAME = os.environ.get('MAIL_USERNAME')
    MAIL_PASSWORD = os.environ.get('MAIL_PASSWORD')
    
    # 系统配置
    SYSTEM_NAME = '黔南民族师范学院学生情绪分析系统'
    SYSTEM_VERSION = '1.0.0'
    ADMIN_EMAIL = '<EMAIL>'
    
    # 情绪标签映射
    EMOTION_LABELS = {
        0: "恐惧",
        1: "愤怒", 
        2: "厌恶",
        3: "喜好",
        4: "悲伤",
        5: "高兴",
        6: "惊讶"
    }
    
    EMOTION_COLORS = {
        "恐惧": "#8B4513",    # 棕色
        "愤怒": "#FF4500",    # 橙红色
        "厌恶": "#9ACD32",    # 黄绿色
        "喜好": "#FF69B4",    # 热粉色
        "悲伤": "#4169E1",    # 皇家蓝
        "高兴": "#FFD700",    # 金色
        "惊讶": "#9370DB"     # 中紫色
    }
    
    @staticmethod
    def init_app(app):
        """初始化应用配置"""
        pass

class DevelopmentConfig(Config):
    """开发环境配置"""
    DEBUG = True
    TESTING = False
    LOG_LEVEL = 'DEBUG'

class TestingConfig(Config):
    """测试环境配置"""
    TESTING = True
    DEBUG = True
    SQLALCHEMY_DATABASE_URI = 'sqlite:///:memory:'
    WTF_CSRF_ENABLED = False
    DAILY_ANALYSIS_LIMIT = 1000

class ProductionConfig(Config):
    """生产环境配置"""
    DEBUG = False
    TESTING = False
    LOG_LEVEL = 'WARNING'
    
    @classmethod
    def init_app(cls, app):
        Config.init_app(app)
        
        # 生产环境特定配置
        import logging
        from logging.handlers import RotatingFileHandler
        
        if not os.path.exists('logs'):
            os.mkdir('logs')
            
        file_handler = RotatingFileHandler(
            'logs/emotion_analysis.log', 
            maxBytes=10240000, 
            backupCount=10
        )
        file_handler.setFormatter(logging.Formatter(
            '%(asctime)s %(levelname)s: %(message)s [in %(pathname)s:%(lineno)d]'
        ))
        file_handler.setLevel(logging.INFO)
        app.logger.addHandler(file_handler)
        
        app.logger.setLevel(logging.INFO)
        app.logger.info('情绪分析系统启动')

# 配置字典
config = {
    'development': DevelopmentConfig,
    'testing': TestingConfig,
    'production': ProductionConfig,
    'default': DevelopmentConfig
}
