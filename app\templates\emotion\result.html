{% extends "base.html" %}

{% block title %}分析结果 - {{ SYSTEM_NAME }}{% endblock %}

{% block content %}
<div class="row">
    <div class="col-lg-8 mx-auto">
        <!-- 分析结果卡片 -->
        <div class="card shadow mb-4">
            <div class="card-header bg-success text-white">
                <h4 class="mb-0">
                    <i class="fas fa-check-circle me-2"></i>情绪分析结果
                </h4>
            </div>
            <div class="card-body">
                <!-- 原始文本 -->
                <div class="mb-4">
                    <h6 class="text-muted mb-2">
                        <i class="fas fa-quote-left me-2"></i>分析文本
                    </h6>
                    <div class="p-3 bg-light rounded">
                        {{ record.text_content }}
                    </div>
                </div>
                
                <!-- 主要情绪结果 -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="text-center p-4 bg-primary text-white rounded">
                            <i class="fas fa-heart fa-3x mb-3"></i>
                            <h3 class="mb-2">{{ record.dominant_emotion }}</h3>
                            <p class="mb-0">主要情绪</p>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="text-center p-4 bg-info text-white rounded">
                            <i class="fas fa-percentage fa-3x mb-3"></i>
                            <h3 class="mb-2">{{ (record.confidence_score * 100) | round(1) }}%</h3>
                            <p class="mb-0">置信度</p>
                        </div>
                    </div>
                </div>
                
                <!-- 详细分数 -->
                <div class="mb-4">
                    <h6 class="mb-3">
                        <i class="fas fa-chart-bar me-2"></i>各情绪分数详情
                    </h6>
                    {% set emotion_colors = {
                        '高兴': 'warning',
                        '悲伤': 'primary', 
                        '愤怒': 'danger',
                        '恐惧': 'dark',
                        '厌恶': 'success',
                        '喜好': 'info',
                        '惊讶': 'secondary'
                    } %}
                    
                    {% for emotion, score in record.get_emotion_scores().items() %}
                    <div class="mb-3">
                        <div class="d-flex justify-content-between align-items-center mb-1">
                            <span class="fw-bold">
                                <span class="badge bg-{{ emotion_colors.get(emotion, 'secondary') }} me-2">
                                    {{ emotion }}
                                </span>
                            </span>
                            <span class="text-muted">{{ (score * 100) | round(2) }}%</span>
                        </div>
                        <div class="progress" style="height: 10px;">
                            <div class="progress-bar bg-{{ emotion_colors.get(emotion, 'secondary') }}" 
                                 role="progressbar" 
                                 style="width: {{ (score * 100) | round(1) }}%"
                                 aria-valuenow="{{ (score * 100) | round(1) }}" 
                                 aria-valuemin="0" 
                                 aria-valuemax="100">
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                
                <!-- 分析信息 -->
                <div class="row">
                    <div class="col-md-6">
                        <div class="card bg-light">
                            <div class="card-body">
                                <h6 class="card-title">
                                    <i class="fas fa-info-circle me-2"></i>分析信息
                                </h6>
                                <ul class="list-unstyled mb-0 small">
                                    <li><strong>分析时间:</strong> {{ record.analysis_time.strftime('%Y-%m-%d %H:%M:%S') }}</li>
                                    <li><strong>文本长度:</strong> {{ record.text_content | length }} 字符</li>
                                    <li><strong>记录ID:</strong> {{ record.id }}</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card bg-light">
                            <div class="card-body">
                                <h6 class="card-title">
                                    <i class="fas fa-lightbulb me-2"></i>情绪解读
                                </h6>
                                <div class="small">
                                    {% if record.dominant_emotion == '高兴' %}
                                    <p class="mb-0 text-success">
                                        <i class="fas fa-smile me-1"></i>
                                        检测到积极正面的情绪，保持这种良好的心态！
                                    </p>
                                    {% elif record.dominant_emotion == '悲伤' %}
                                    <p class="mb-0 text-primary">
                                        <i class="fas fa-frown me-1"></i>
                                        检测到低落情绪，建议适当调节心情，寻求支持。
                                    </p>
                                    {% elif record.dominant_emotion == '愤怒' %}
                                    <p class="mb-0 text-danger">
                                        <i class="fas fa-angry me-1"></i>
                                        检测到愤怒情绪，建议冷静处理，避免冲动行为。
                                    </p>
                                    {% elif record.dominant_emotion == '恐惧' %}
                                    <p class="mb-0 text-dark">
                                        <i class="fas fa-exclamation-triangle me-1"></i>
                                        检测到恐惧情绪，建议寻求帮助，面对困难。
                                    </p>
                                    {% elif record.dominant_emotion == '厌恶' %}
                                    <p class="mb-0 text-success">
                                        <i class="fas fa-thumbs-down me-1"></i>
                                        检测到厌恶情绪，建议远离负面因素。
                                    </p>
                                    {% elif record.dominant_emotion == '喜好' %}
                                    <p class="mb-0 text-info">
                                        <i class="fas fa-heart me-1"></i>
                                        检测到喜爱情绪，继续保持对美好事物的热爱。
                                    </p>
                                    {% elif record.dominant_emotion == '惊讶' %}
                                    <p class="mb-0 text-secondary">
                                        <i class="fas fa-surprise me-1"></i>
                                        检测到惊讶情绪，保持对新事物的好奇心。
                                    </p>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 操作按钮 -->
        <div class="text-center mb-4">
            <a href="{{ url_for('emotion.analyze') }}" class="btn btn-primary me-2">
                <i class="fas fa-search me-2"></i>继续分析
            </a>
            <a href="{{ url_for('emotion.history') }}" class="btn btn-outline-secondary me-2">
                <i class="fas fa-history me-2"></i>查看历史
            </a>
            <a href="{{ url_for('emotion.statistics') }}" class="btn btn-outline-info">
                <i class="fas fa-chart-bar me-2"></i>统计分析
            </a>
        </div>
        
        <!-- 相关建议 -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-compass me-2"></i>相关建议
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6><i class="fas fa-book me-2"></i>学习建议</h6>
                        <ul class="small">
                            {% if record.dominant_emotion in ['高兴', '喜好'] %}
                            <li>保持积极的学习态度</li>
                            <li>可以尝试更有挑战性的内容</li>
                            <li>分享学习心得给同学</li>
                            {% elif record.dominant_emotion in ['悲伤', '恐惧'] %}
                            <li>适当降低学习强度</li>
                            <li>寻求老师或同学的帮助</li>
                            <li>制定更合理的学习计划</li>
                            {% elif record.dominant_emotion == '愤怒' %}
                            <li>暂时休息，调整心态</li>
                            <li>找出愤怒的根源并解决</li>
                            <li>避免在情绪激动时做决定</li>
                            {% else %}
                            <li>保持正常的学习节奏</li>
                            <li>注意劳逸结合</li>
                            <li>定期回顾学习进度</li>
                            {% endif %}
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6><i class="fas fa-heart me-2"></i>心理健康</h6>
                        <ul class="small">
                            {% if record.dominant_emotion in ['悲伤', '恐惧', '愤怒'] %}
                            <li>考虑寻求心理咨询</li>
                            <li>与朋友或家人交流</li>
                            <li>进行适当的体育锻炼</li>
                            <li>保持规律的作息时间</li>
                            {% else %}
                            <li>继续保持良好心态</li>
                            <li>定期进行自我反思</li>
                            <li>培养兴趣爱好</li>
                            <li>维护良好的人际关系</li>
                            {% endif %}
                        </ul>
                    </div>
                </div>
                
                {% if record.dominant_emotion in ['悲伤', '恐惧', '愤怒'] %}
                <div class="alert alert-info mt-3">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>温馨提示:</strong> 如果持续出现负面情绪，建议联系学校心理咨询中心寻求专业帮助。
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// 页面加载完成后的动画效果
document.addEventListener('DOMContentLoaded', function() {
    // 进度条动画
    const progressBars = document.querySelectorAll('.progress-bar');
    progressBars.forEach(bar => {
        const width = bar.style.width;
        bar.style.width = '0%';
        setTimeout(() => {
            bar.style.transition = 'width 1s ease-in-out';
            bar.style.width = width;
        }, 100);
    });
});
</script>
{% endblock %}
