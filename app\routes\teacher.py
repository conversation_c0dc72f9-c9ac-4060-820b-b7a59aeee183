"""
教师端路由
"""
from flask import Blueprint, render_template, request, flash, redirect, url_for, jsonify
from flask_login import login_required, current_user
from sqlalchemy import func, desc
from datetime import datetime, timedelta
from app import db
from app.models.user import User
from app.models.emotion_record import EmotionRecord

teacher_bp = Blueprint('teacher', __name__, url_prefix='/teacher')

def teacher_required(f):
    """教师权限装饰器"""
    def decorated_function(*args, **kwargs):
        if not current_user.is_authenticated or current_user.role not in ['teacher', 'admin']:
            flash('需要教师权限才能访问此页面', 'error')
            return redirect(url_for('main.index'))
        return f(*args, **kwargs)
    decorated_function.__name__ = f.__name__
    return decorated_function

@teacher_bp.route('/dashboard')
@login_required
@teacher_required
def dashboard():
    """教师仪表板"""
    # 获取本专业学生
    students = User.query.filter_by(
        role='student',
        department=current_user.department,
        major=current_user.major
    ).all()
    
    # 统计数据
    total_students = len(students)
    student_ids = [s.id for s in students]
    
    # 本专业学生总分析次数
    total_records = EmotionRecord.query.filter(
        EmotionRecord.user_id.in_(student_ids)
    ).count() if student_ids else 0
    
    # 今日分析次数
    today = datetime.now().date()
    today_records = EmotionRecord.query.filter(
        EmotionRecord.user_id.in_(student_ids),
        func.date(EmotionRecord.analysis_time) == today
    ).count() if student_ids else 0
    
    # 本周活跃学生
    week_ago = datetime.now() - timedelta(days=7)
    active_students = db.session.query(
        User.id, User.username, User.real_name, User.student_id,
        func.count(EmotionRecord.id).label('analysis_count')
    ).join(EmotionRecord).filter(
        User.id.in_(student_ids),
        EmotionRecord.analysis_time >= week_ago
    ).group_by(User.id).order_by(desc('analysis_count')).limit(10).all() if student_ids else []
    
    # 情绪分布统计
    emotion_stats = db.session.query(
        EmotionRecord.dominant_emotion,
        func.count(EmotionRecord.id).label('count')
    ).filter(
        EmotionRecord.user_id.in_(student_ids)
    ).group_by(EmotionRecord.dominant_emotion).all() if student_ids else []
    
    # 最近7天趋势
    daily_stats = []
    for i in range(7):
        date = datetime.now().date() - timedelta(days=i)
        count = EmotionRecord.query.filter(
            EmotionRecord.user_id.in_(student_ids),
            func.date(EmotionRecord.analysis_time) == date
        ).count() if student_ids else 0
        daily_stats.append({'date': date, 'count': count})
    daily_stats.reverse()
    
    # 需要关注的学生（消极情绪较多）
    concern_students = []
    if student_ids:
        for student in students:
            negative_count = EmotionRecord.query.filter(
                EmotionRecord.user_id == student.id,
                EmotionRecord.dominant_emotion.in_(['悲伤', '愤怒', '恐惧', '厌恶']),
                EmotionRecord.analysis_time >= week_ago
            ).count()
            
            total_count = EmotionRecord.query.filter(
                EmotionRecord.user_id == student.id,
                EmotionRecord.analysis_time >= week_ago
            ).count()
            
            if total_count > 0 and negative_count / total_count > 0.6:
                concern_students.append({
                    'student': student,
                    'negative_ratio': negative_count / total_count,
                    'total_count': total_count
                })
    
    concern_students.sort(key=lambda x: x['negative_ratio'], reverse=True)
    
    return render_template('teacher/dashboard.html',
                         total_students=total_students,
                         total_records=total_records,
                         today_records=today_records,
                         active_students=active_students,
                         emotion_stats=emotion_stats,
                         daily_stats=daily_stats,
                         concern_students=concern_students[:5])

@teacher_bp.route('/students')
@login_required
@teacher_required
def students():
    """学生列表"""
    page = request.args.get('page', 1, type=int)
    per_page = 20
    
    # 获取本专业学生
    students_query = User.query.filter_by(
        role='student',
        department=current_user.department,
        major=current_user.major
    ).order_by(User.student_id)
    
    students = students_query.paginate(
        page=page, per_page=per_page, error_out=False
    )
    
    # 为每个学生添加统计信息
    for student in students.items:
        # 总分析次数
        student.total_analysis = EmotionRecord.query.filter_by(user_id=student.id).count()
        
        # 本周分析次数
        week_ago = datetime.now() - timedelta(days=7)
        student.week_analysis = EmotionRecord.query.filter(
            EmotionRecord.user_id == student.id,
            EmotionRecord.analysis_time >= week_ago
        ).count()
        
        # 最近情绪状态
        recent_record = EmotionRecord.query.filter_by(user_id=student.id)\
            .order_by(EmotionRecord.analysis_time.desc()).first()
        student.recent_emotion = recent_record.dominant_emotion if recent_record else None
        student.recent_time = recent_record.analysis_time if recent_record else None
    
    return render_template('teacher/students.html', students=students)

@teacher_bp.route('/student/<int:student_id>')
@login_required
@teacher_required
def student_detail(student_id):
    """学生详情"""
    student = User.query.get_or_404(student_id)
    
    # 检查权限：只能查看本专业学生
    if (student.role != 'student' or 
        student.department != current_user.department or 
        student.major != current_user.major):
        flash('无权限查看此学生信息', 'error')
        return redirect(url_for('teacher.students'))
    
    # 获取时间范围
    days = request.args.get('days', 30, type=int)
    start_date = datetime.now() - timedelta(days=days)
    
    # 学生分析记录
    records = EmotionRecord.query.filter(
        EmotionRecord.user_id == student_id,
        EmotionRecord.analysis_time >= start_date
    ).order_by(EmotionRecord.analysis_time.desc()).limit(50).all()
    
    # 情绪统计
    emotion_stats = db.session.query(
        EmotionRecord.dominant_emotion,
        func.count(EmotionRecord.id).label('count')
    ).filter(
        EmotionRecord.user_id == student_id,
        EmotionRecord.analysis_time >= start_date
    ).group_by(EmotionRecord.dominant_emotion).all()
    
    emotion_dict = {stat.dominant_emotion: stat.count for stat in emotion_stats}
    total_count = sum(emotion_dict.values())
    
    # 情绪趋势（按天统计）
    trend_data = {}
    for i in range(min(days, 30)):  # 最多显示30天
        date = (datetime.now() - timedelta(days=i)).date()
        date_str = date.strftime('%Y-%m-%d')
        
        day_records = EmotionRecord.query.filter(
            EmotionRecord.user_id == student_id,
            func.date(EmotionRecord.analysis_time) == date
        ).all()
        
        day_emotions = {}
        for record in day_records:
            emotion = record.dominant_emotion
            day_emotions[emotion] = day_emotions.get(emotion, 0) + 1
        
        trend_data[date_str] = day_emotions
    
    # 心理健康评估
    positive_emotions = emotion_dict.get('高兴', 0) + emotion_dict.get('喜好', 0)
    negative_emotions = (emotion_dict.get('悲伤', 0) + emotion_dict.get('愤怒', 0) + 
                        emotion_dict.get('恐惧', 0) + emotion_dict.get('厌恶', 0))
    
    health_status = 'unknown'
    if total_count > 0:
        positive_ratio = positive_emotions / total_count
        negative_ratio = negative_emotions / total_count
        
        if positive_ratio > 0.4 and negative_ratio < 0.3:
            health_status = 'good'
        elif positive_ratio > 0.2 and negative_ratio < 0.5:
            health_status = 'normal'
        else:
            health_status = 'concern'
    
    return render_template('teacher/student_detail.html',
                         student=student,
                         records=records,
                         emotion_stats=emotion_dict,
                         total_count=total_count,
                         trend_data=trend_data,
                         health_status=health_status,
                         days=days)

@teacher_bp.route('/analytics')
@login_required
@teacher_required
def analytics():
    """专业分析报告"""
    # 获取本专业学生
    students = User.query.filter_by(
        role='student',
        department=current_user.department,
        major=current_user.major
    ).all()
    
    student_ids = [s.id for s in students]
    
    # 时间范围
    days = request.args.get('days', 30, type=int)
    start_date = datetime.now() - timedelta(days=days)
    
    # 整体情绪分布
    emotion_stats = db.session.query(
        EmotionRecord.dominant_emotion,
        func.count(EmotionRecord.id).label('count')
    ).filter(
        EmotionRecord.user_id.in_(student_ids),
        EmotionRecord.analysis_time >= start_date
    ).group_by(EmotionRecord.dominant_emotion).all() if student_ids else []
    
    # 按年级统计
    grade_stats = {}
    for student in students:
        if student.grade:
            if student.grade not in grade_stats:
                grade_stats[student.grade] = {
                    'student_count': 0,
                    'analysis_count': 0,
                    'emotions': {}
                }
            
            grade_stats[student.grade]['student_count'] += 1
            
            # 该年级学生的分析记录
            student_records = EmotionRecord.query.filter(
                EmotionRecord.user_id == student.id,
                EmotionRecord.analysis_time >= start_date
            ).all()
            
            grade_stats[student.grade]['analysis_count'] += len(student_records)
            
            for record in student_records:
                emotion = record.dominant_emotion
                if emotion not in grade_stats[student.grade]['emotions']:
                    grade_stats[student.grade]['emotions'][emotion] = 0
                grade_stats[student.grade]['emotions'][emotion] += 1
    
    # 活跃度统计
    activity_stats = []
    for student in students:
        count = EmotionRecord.query.filter(
            EmotionRecord.user_id == student.id,
            EmotionRecord.analysis_time >= start_date
        ).count()
        
        if count > 0:
            activity_stats.append({
                'student': student,
                'count': count
            })
    
    activity_stats.sort(key=lambda x: x['count'], reverse=True)
    
    return render_template('teacher/analytics.html',
                         students=students,
                         emotion_stats=emotion_stats,
                         grade_stats=grade_stats,
                         activity_stats=activity_stats[:20],
                         days=days)

@teacher_bp.route('/export')
@login_required
@teacher_required
def export_data():
    """导出数据"""
    flash('数据导出功能开发中', 'info')
    return redirect(url_for('teacher.dashboard'))

@teacher_bp.route('/api/student_emotion_trend/<int:student_id>')
@login_required
@teacher_required
def api_student_emotion_trend(student_id):
    """获取学生情绪趋势API"""
    student = User.query.get_or_404(student_id)
    
    # 检查权限
    if (student.role != 'student' or 
        student.department != current_user.department or 
        student.major != current_user.major):
        return jsonify({'error': '无权限'}), 403
    
    days = request.args.get('days', 7, type=int)
    
    trend_data = []
    for i in range(days):
        date = datetime.now().date() - timedelta(days=i)
        
        day_records = EmotionRecord.query.filter(
            EmotionRecord.user_id == student_id,
            func.date(EmotionRecord.analysis_time) == date
        ).all()
        
        emotions = {}
        for record in day_records:
            emotion = record.dominant_emotion
            emotions[emotion] = emotions.get(emotion, 0) + 1
        
        trend_data.append({
            'date': date.strftime('%Y-%m-%d'),
            'emotions': emotions,
            'total': len(day_records)
        })
    
    trend_data.reverse()
    return jsonify(trend_data)
