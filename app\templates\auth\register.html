{% extends "base.html" %}

{% block title %}用户注册 - {{ SYSTEM_NAME }}{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-8 col-lg-6">
        <div class="card shadow">
            <div class="card-header text-center bg-success text-white">
                <h4 class="mb-0">
                    <i class="fas fa-user-plus me-2"></i>用户注册
                </h4>
            </div>
            <div class="card-body">
                <form method="POST">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="username" class="form-label">用户名 <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="username" name="username" 
                                   placeholder="请输入用户名" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="real_name" class="form-label">真实姓名 <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="real_name" name="real_name" 
                                   placeholder="请输入真实姓名" required>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="student_id" class="form-label">学号 <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="student_id" name="student_id" 
                                   placeholder="请输入学号" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="email" class="form-label">邮箱 <span class="text-danger">*</span></label>
                            <input type="email" class="form-control" id="email" name="email" 
                                   placeholder="请输入邮箱地址" required>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="department" class="form-label">院系</label>
                            <select class="form-select" id="department" name="department">
                                <option value="">请选择院系</option>
                                <option value="计算机科学学院">计算机科学学院</option>
                                <option value="数学与统计学院">数学与统计学院</option>
                                <option value="物理与电子科学学院">物理与电子科学学院</option>
                                <option value="化学化工学院">化学化工学院</option>
                                <option value="生物科学学院">生物科学学院</option>
                                <option value="文学与传媒学院">文学与传媒学院</option>
                                <option value="外国语学院">外国语学院</option>
                                <option value="历史与社会学院">历史与社会学院</option>
                                <option value="马克思主义学院">马克思主义学院</option>
                                <option value="经济与管理学院">经济与管理学院</option>
                                <option value="教育科学学院">教育科学学院</option>
                                <option value="体育学院">体育学院</option>
                                <option value="音乐舞蹈学院">音乐舞蹈学院</option>
                                <option value="美术学院">美术学院</option>
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="major" class="form-label">专业</label>
                            <input type="text" class="form-control" id="major" name="major" 
                                   placeholder="请输入专业名称">
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="grade" class="form-label">年级</label>
                        <select class="form-select" id="grade" name="grade">
                            <option value="">请选择年级</option>
                            <option value="2024级">2024级</option>
                            <option value="2023级">2023级</option>
                            <option value="2022级">2022级</option>
                            <option value="2021级">2021级</option>
                        </select>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="password" class="form-label">密码 <span class="text-danger">*</span></label>
                            <input type="password" class="form-control" id="password" name="password" 
                                   placeholder="请输入密码(至少6位)" required minlength="6">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="confirm_password" class="form-label">确认密码 <span class="text-danger">*</span></label>
                            <input type="password" class="form-control" id="confirm_password" name="confirm_password" 
                                   placeholder="请再次输入密码" required minlength="6">
                        </div>
                    </div>
                    
                    <div class="mb-3 form-check">
                        <input type="checkbox" class="form-check-input" id="agree" required>
                        <label class="form-check-label" for="agree">
                            我已阅读并同意<a href="#" class="text-decoration-none">用户协议</a>和<a href="#" class="text-decoration-none">隐私政策</a>
                        </label>
                    </div>
                    
                    <div class="d-grid">
                        <button type="submit" class="btn btn-success">
                            <i class="fas fa-user-plus me-2"></i>注册账户
                        </button>
                    </div>
                </form>
            </div>
            <div class="card-footer text-center">
                <p class="mb-0">
                    已有账户？
                    <a href="{{ url_for('auth.login') }}" class="text-decoration-none">立即登录</a>
                </p>
            </div>
        </div>
    </div>
</div>

<script>
// 密码确认验证
document.getElementById('confirm_password').addEventListener('input', function() {
    const password = document.getElementById('password').value;
    const confirmPassword = this.value;
    
    if (password !== confirmPassword) {
        this.setCustomValidity('两次输入的密码不一致');
    } else {
        this.setCustomValidity('');
    }
});
</script>
{% endblock %}
